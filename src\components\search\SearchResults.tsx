"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import Image from "next/image";
import { Game, SearchParams, searchGames } from "@/lib/apiService";

interface SearchResultsProps {
  initialQuery: string;
  initialResults?: Game[];
  initialTotal?: number;
}

export default function SearchResults({ 
  initialQuery, 
  initialResults = [], 
  initialTotal = 0 
}: SearchResultsProps) {
  const [query, setQuery] = useState(initialQuery);
  const [results, setResults] = useState<Game[]>(initialResults);
  const [total, setTotal] = useState(initialTotal);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [loadingTimeout, setLoadingTimeout] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const limit = 12;

  // Update search results when query or page changes
  useEffect(() => {
    if (query.trim()) {
      performSearch();
    }
    
    // Cleanup function
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [query, page]);

  const performSearch = async () => {
    setLoading(true);
    setError(null);
    setLoadingTimeout(false);
    
    // Set a timeout detection
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      setLoadingTimeout(true);
    }, 10000);
    
    try {
      const params: SearchParams = {
        keyword: query,
        skip: (page - 1) * limit,
        limit
      };

      const searchResult = await searchGames(params);
      
      if (searchResult.status === "success") {
        setResults(searchResult.data);
        setTotal(searchResult.total);
      } else {
        setResults([]);
        setTotal(0);
        setError("Search request failed. Please try again later.");
      }
    } catch (error) {
      console.error("Search error:", error);
      setError("An error occurred during search. Please try again later.");
      setResults([]);
      setTotal(0);
    } finally {
      setLoading(false);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      setLoadingTimeout(false);
    }
  };

  const totalPages = Math.ceil(total / limit);

  return (
    <div>
      {/* 搜索结果顶部区域 */}
      <div className="relative mb-8 bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg overflow-hidden">
        <div className="absolute inset-0 opacity-10 bg-pattern"></div>
        <div className="relative z-10 px-6 py-8 md:px-10 text-white">
          <h2 className="text-2xl md:text-3xl font-bold mb-3">
            Search results: <span className="text-blue-200">"{query}"</span>
          </h2>
          <p className="text-blue-100">
            Found {total} games matching your search. Browse all results below.
          </p>
        </div>
      </div>

      <div className="w-full">
        {loading && (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
            {Array.from({ length: limit }).map((_, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
                <div className="aspect-[4/3] bg-blue-100"></div>
                <div className="p-4">
                  <div className="h-5 bg-blue-100 rounded-full w-3/4 mb-3"></div>
                  <div className="h-4 bg-gray-100 rounded-full w-1/2 mb-2"></div>
                  <div className="flex justify-between mt-3">
                    <div className="h-6 w-16 bg-blue-50 rounded-full"></div>
                    <div className="h-6 w-6 bg-green-50 rounded-full"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {error && !loading && (
          <div className="text-center py-16 bg-white rounded-xl shadow-md p-8 max-w-2xl mx-auto">
            <div className="bg-red-50 p-6 rounded-lg mb-6">
              <svg className="w-16 h-16 text-red-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h2 className="text-2xl font-bold text-red-600 mb-2">Search Error</h2>
              <p className="text-gray-600 mb-6">{error}</p>
            </div>
            <button 
              onClick={performSearch}
              className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-8 rounded-lg transition-colors"
            >
              Retry Search
            </button>
          </div>
        )}

        {!loading && !error && results.length === 0 && (
          <div className="text-center py-16 bg-white rounded-xl shadow-md p-8 max-w-2xl mx-auto">
            <div className="bg-blue-50 p-6 rounded-lg mb-6">
              <svg className="w-16 h-16 text-blue-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h2 className="text-2xl font-bold text-blue-600 mb-2">No Results Found</h2>
              <p className="text-gray-600 mb-3">No games found matching "{query}"</p>
              <p className="text-sm text-gray-500">Please try different keywords or browse our categories</p>
            </div>
          </div>
        )}

        {results.length > 0 && (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
            {results.map((game) => (
              <Link 
                href={`/games/${game.game_name}`} 
                key={game.game_id}
              >
                <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:scale-105 h-full flex flex-col">
                  <div className="relative aspect-[4/3] overflow-hidden">
                    <Image
                      src={game.image}
                      alt={game.name}
                      fill
                      className="object-cover hover:scale-110 transition-transform duration-500"
                      sizes="(max-width: 768px) 100vw, 300px"
                    />
                    {game.rating && (
                      <div className="absolute top-2 right-2 bg-green-600 text-white text-xs font-medium px-2 py-1 rounded-md flex items-center">
                        <svg className="w-3 h-3 mr-1 text-yellow-300" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        {game.rating}
                      </div>
                    )}
                  </div>
                  <div className="p-4 flex-grow flex flex-col">
                    <h3 className="font-semibold text-gray-900 mb-1 truncate">{game.name}</h3>
                    <div className="text-xs mb-2 flex-grow">
                      {game.category_name && (
                        <span className="bg-blue-50 text-blue-600 px-2 py-0.5 rounded-full text-xs">
                          {game.category_name}
                        </span>
                      )}
                    </div>
                    <div className="flex items-center justify-between mt-auto">
                      <span className="text-blue-600 text-sm font-medium inline-flex items-center group">
                        Play Now
                        <svg className="w-4 h-4 ml-1 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                        </svg>
                      </span>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}

        {totalPages > 1 && (
          <div className="flex flex-col items-center mt-10 mb-6">
            <div className="flex justify-center items-center space-x-2 bg-white p-3 rounded-lg shadow-md">
              <button 
                onClick={() => setPage(Math.max(1, page - 1))}
                disabled={page === 1}
                className={`px-4 py-2 rounded-md transition-all duration-200 font-medium ${
                  page === 1 
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                    : 'bg-blue-100 text-blue-700 hover:bg-blue-200 hover:scale-105'
                }`}
                aria-label="Previous page"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              
              {/* Page number buttons */}
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const pageNum = page <= 3 
                  ? i + 1 
                  : page >= totalPages - 2 
                    ? totalPages - 4 + i 
                    : page - 2 + i;
                
                if (pageNum <= totalPages) {
                  return (
                    <button
                      key={`page-${pageNum}`}
                      onClick={() => setPage(pageNum)}
                      className={`px-4 py-2 rounded-md transition-all duration-200 ${
                        pageNum === page 
                          ? 'bg-blue-600 text-white font-bold scale-110' 
                          : 'bg-gray-100 hover:bg-gray-200 text-gray-700 hover:scale-105'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                }
                return null;
              })}
              
              <button
                onClick={() => setPage(Math.min(totalPages, page + 1))}
                disabled={page === totalPages}
                className={`px-4 py-2 rounded-md transition-all duration-200 font-medium ${
                  page === totalPages 
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                    : 'bg-blue-100 text-blue-700 hover:bg-blue-200 hover:scale-105'
                }`}
                aria-label="Next page"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 