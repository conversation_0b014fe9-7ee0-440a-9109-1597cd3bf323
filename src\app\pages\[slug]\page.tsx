import { getPageDescription } from "@/lib/apiService";
import { notFound } from "next/navigation";
import { Metadata } from "next";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import CMSPageContent from "./CMSPageContent";

// HTML内容处理函数
function sanitizeHtml(html: string): string {
  if (!html) return '';

  // 替换转义的HTML字符
  return html
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/&amp;/g, '&');
}

// 定义页面内容的接口
interface PageContent {
  title: string;
  content: string;
  sections: {
    title: string;
    content: string;
  }[];
}

// 有效的页面类型
type ValidPageSlug = 'about' | 'contact' | 'privacy' | 'terms' | 'faq' | 'help';

// 页面标题映射
const PAGE_MAPPING: Record<ValidPageSlug, string> = {
  about: 'About Us',
  contact: 'Contact Us',
  privacy: 'Privacy Policy',
  terms: 'Terms of Service',
  faq: 'Frequently Asked Questions',
  help: 'Help Center',
};

// 验证slug是否有效
function isValidSlug(slug: string): slug is ValidPageSlug {
  return Object.keys(PAGE_MAPPING).includes(slug);
}

// 定义参数接口
interface PageParams {
  params: Promise<{ slug: string }> | { slug: string };
}

// 生成静态参数用于预渲染页面
export async function generateStaticParams() {
  // 返回所有有效的页面slug
  return Object.keys(PAGE_MAPPING).map((slug) => ({
    slug: slug,
  }));
}

// 生成元数据
export async function generateMetadata({ params }: PageParams): Promise<Metadata> {
  // 解析params
  const resolvedParams = await params;
  const { slug } = resolvedParams;

  // 验证slug
  if (!isValidSlug(slug)) {
    return {
      title: 'Page Not Found',
      description: 'The requested page could not be found.',
    };
  }

  // 使用类型断言确保TypeScript知道此时slug是ValidPageSlug类型
  const validSlug = slug as ValidPageSlug;
  return {
    title: `${PAGE_MAPPING[validSlug]} | Drive Mad`,
    description: `${PAGE_MAPPING[validSlug]} - Information about Drive Mad.`,
  };
}

export default async function CMSPage({ params }: PageParams) {
  // 解析params
  const resolvedParams = await params;
  const { slug } = resolvedParams;

  console.log(`[CMS Page] Rendering page for slug: ${slug}`);

  // 验证slug是否有效
  if (!isValidSlug(slug)) {
    console.log(`[CMS Page] Invalid slug: ${slug}. Redirecting to 404.`);
    notFound();
  }

  // 使用类型断言确保TypeScript知道此时slug是ValidPageSlug类型
  const validSlug = slug as ValidPageSlug;

  // 获取页面内容
  let pageContent: PageContent = {
    title: PAGE_MAPPING[validSlug] || "Page Content",
    content: "",
    sections: []
  };

  let error: string | null = null;

  try {
    const pageUrl = `/${slug}`;
    console.log(`[CMS Page] Fetching content for ${pageUrl}`);

    const result = await getPageDescription(pageUrl);
    console.log(`[CMS Page] API response:`, result);

    if (result.status === "success") {
      // 设置默认内容为页面的描述
      pageContent.content = result.data.description || "";

      // 解析content_value中的内容
      try {
        if (result.data.has_content === "1" && result.data.content_value) {
          console.log(`[CMS Page] Page has content_value, attempting to parse as JSON`);
          // 尝试解析JSON内容
          const contentData = JSON.parse(result.data.content_value);
          pageContent = {
            title: contentData.title || result.data.description || PAGE_MAPPING[validSlug],
            content: contentData.content || "",
            sections: Array.isArray(contentData.sections) ? contentData.sections : []
          };
        } else if (result.data.content_value) {
          // 如果不是JSON格式，直接使用content_value作为内容
          console.log(`[CMS Page] Using content_value as plain text`);
          pageContent.content = result.data.content_value;
        } else if (result.data.description) {
          // 如果没有content_value，则使用description
          console.log(`[CMS Page] No content_value, using description`);
          pageContent.content = result.data.description;
        }
      } catch (parseError) {
        // 解析JSON失败时，使用content_value作为普通文本
        console.error(`[CMS Page] Failed to parse content_value as JSON:`, parseError);
        pageContent.content = result.data.content_value || result.data.description || "";
      }

      // 如果完全没有内容，添加一个默认的占位内容
      if (!pageContent.content && !pageContent.title) {
        console.log(`[CMS Page] No content found, using default title`);
        pageContent.title = PAGE_MAPPING[validSlug];
      }
    } else {
      console.error(`[CMS Page] API returned error status:`, result);
      error = "Could not load page content. Please try again later.";
    }
  } catch (fetchError) {
    console.error(`[CMS Page] Error fetching ${slug} page content:`, fetchError);
    error = "An error occurred while loading the page. Please try again later.";
  }

  // 打印最终要渲染的内容，用于调试
  console.log(`[CMS Page] Final page content to render:`, {
    title: pageContent.title,
    contentLength: pageContent.content?.length || 0,
    sectionsCount: pageContent.sections?.length || 0,
    error
  });

  // 为页面内容应用sanitizeHtml
  const sanitizedPageContent: PageContent = {
    title: sanitizeHtml(pageContent.title),
    content: sanitizeHtml(pageContent.content),
    sections: pageContent.sections.map(section => ({
      title: sanitizeHtml(section.title),
      content: sanitizeHtml(section.content)
    }))
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">


          {/* Content Area */}
          <CMSPageContent
            pageContent={sanitizedPageContent}
            error={error}
          />
        </div>
      </main>
      <Footer />
    </div>
  );
}