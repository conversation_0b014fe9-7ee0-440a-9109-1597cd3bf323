"use client";

import { useState, useEffect } from 'react';
import { getCategories, getTags, Category, Tag } from '@/lib/apiService';

interface SearchFiltersProps {
  onCategoryChange: (categoryId?: number) => void;
  onTagChange: (tagId?: number) => void;
  selectedCategory?: number;
  selectedTag?: number;
}

export default function SearchFilters({
  onCategoryChange,
  onTagChange,
  selectedCategory,
  selectedTag
}: SearchFiltersProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [showCategoryFilter, setShowCategoryFilter] = useState(true);
  const [showTagFilter, setShowTagFilter] = useState(true);

  useEffect(() => {
    let isMounted = true;
    
    async function fetchFilters() {
      setIsLoading(true);
      setHasError(false);
      
      try {
        // 单独处理每个请求，避免一个请求失败影响另一个
        const categoriesResponse = await getCategories();
        if (isMounted && categoriesResponse.status === 'success') {
          setCategories(categoriesResponse.data);
        }
        
        const tagsResponse = await getTags();
        if (isMounted && tagsResponse.status === 'success') {
          setTags(tagsResponse.data);
        }
      } catch (error) {
        console.error('获取筛选条件出错:', error);
        if (isMounted) {
          setHasError(true);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    }

    fetchFilters();
    
    // 清理函数
    return () => {
      isMounted = false;
    };
  }, []);

  // 加载中的状态显示
  if (isLoading) {
    return (
      <div className="bg-blue-600 text-white rounded-lg p-4 shadow-md animate-pulse">
        <div className="h-6 bg-blue-500 rounded mb-4"></div>
        <div className="space-y-2">
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="h-4 bg-blue-500 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  // 错误状态显示（但由于有默认数据，应该不会进入此状态）
  if (hasError && categories.length === 0 && tags.length === 0) {
    return (
      <div className="bg-blue-600 text-white rounded-lg p-4 shadow-md">
        <h3 className="font-bold mb-2">筛选选项加载失败</h3>
        <p className="text-sm mb-3">无法获取分类和标签数据</p>
        <button 
          onClick={() => window.location.reload()}
          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
        >
          刷新页面
        </button>
      </div>
    );
  }

  return (
    <div className="bg-blue-600 text-white rounded-lg p-4 shadow-md">
      {categories.length > 0 && (
        <div className="mb-6">
          <div 
            className="flex justify-between items-center cursor-pointer mb-2"
            onClick={() => setShowCategoryFilter(!showCategoryFilter)}
          >
            <h3 className="font-bold text-white">游戏分类</h3>
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className={`h-5 w-5 transition-transform ${showCategoryFilter ? 'transform rotate-180' : ''}`}
              viewBox="0 0 20 20" 
              fill="currentColor"
            >
              <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </div>
          
          {showCategoryFilter && (
            <div className="space-y-2 max-h-60 overflow-y-auto">
              <div 
                className={`px-2 py-1 rounded cursor-pointer ${!selectedCategory ? 'bg-green-600 text-white' : 'hover:bg-blue-500'}`}
                onClick={() => onCategoryChange(undefined)}
              >
                全部分类
              </div>
              {categories.map(category => (
                <div 
                  key={category.id}
                  className={`px-2 py-1 rounded cursor-pointer ${selectedCategory === category.id ? 'bg-green-600 text-white' : 'hover:bg-blue-500'}`}
                  onClick={() => onCategoryChange(category.id)}
                >
                  {category.name}
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {tags.length > 0 && (
        <div>
          <div 
            className="flex justify-between items-center cursor-pointer mb-2"
            onClick={() => setShowTagFilter(!showTagFilter)}
          >
            <h3 className="font-bold text-white">游戏标签</h3>
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className={`h-5 w-5 transition-transform ${showTagFilter ? 'transform rotate-180' : ''}`}
              viewBox="0 0 20 20" 
              fill="currentColor"
            >
              <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </div>
          
          {showTagFilter && (
            <div className="space-y-2 max-h-60 overflow-y-auto">
              <div 
                className={`px-2 py-1 rounded cursor-pointer ${!selectedTag ? 'bg-green-600 text-white' : 'hover:bg-blue-500'}`}
                onClick={() => onTagChange(undefined)}
              >
                全部标签
              </div>
              {tags.map(tag => (
                <div 
                  key={tag.id}
                  className={`px-2 py-1 rounded cursor-pointer ${selectedTag === tag.id ? 'bg-green-600 text-white' : 'hover:bg-blue-500'}`}
                  onClick={() => onTagChange(tag.id)}
                >
                  {tag.name}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
} 