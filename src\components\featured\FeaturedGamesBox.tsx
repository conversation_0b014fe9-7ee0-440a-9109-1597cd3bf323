"use client";

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Game, getFeaturedGames } from '@/lib/apiService';

interface FeaturedGamesBoxProps {
  title: string;
  viewAllLink: string;
  limit?: number;
}

export function FeaturedGamesBox({ title, viewAllLink, limit = 6 }: FeaturedGamesBoxProps) {
  const [games, setGames] = useState<Game[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function fetchGames() {
      setIsLoading(true);
      try {
        const result = await getFeaturedGames(limit);
        if (result.status === 'success' && result.data.length > 0) {
          setGames(result.data);
        }
      } catch (error) {
        console.error('Failed to fetch featured games:', error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchGames();
  }, [limit]);

  return (
    <div className="bg-blue-600 rounded-md p-4 flex flex-col h-[300px] md:h-[400px]">
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-white font-bold text-sm">{title}</h3>
        <Link href={viewAllLink} className="bg-orange-500 hover:bg-orange-600 text-white font-bold text-xs px-3 py-1 rounded-md transition-colors">
          View All
        </Link>
      </div>
      
      {isLoading ? (
        // Loading skeleton
        <div className="grid grid-cols-2 gap-2 flex-grow content-between">
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="bg-blue-700/40 rounded-md overflow-hidden h-[70px] md:h-[110px] animate-pulse"></div>
          ))}
        </div>
      ) : games.length === 0 ? (
        // No games found state
        <div className="flex items-center justify-center h-full text-white/80">
          <p>No featured games available</p>
        </div>
      ) : (
        // Games grid
        <div className="grid grid-cols-2 gap-2 flex-grow content-between">
          {games.slice(0, 6).map((game) => (
            <Link key={game.game_id} href={`/games/${game.game_name}`} className="block">
              <div className="bg-blue-700/20 rounded-md overflow-hidden h-[70px] md:h-[110px] relative hover:bg-blue-700/40 transition-colors">
                <Image
                  src={game.image}
                  alt={game.name}
                  fill
                  className="object-cover rounded-md"
                />
              </div>
            </Link>
          ))}
        </div>
      )}
    </div>
  );
} 