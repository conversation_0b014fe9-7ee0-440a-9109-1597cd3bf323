import { MetadataRoute } from 'next'
import { getPopularGames, getLatestGames, getCategories } from '@/lib/apiService'

// 静态导出配置
export const dynamic = 'force-static'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = 'https://drivemad.store'

  // 基础页面
  const staticPages: MetadataRoute.Sitemap = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1,
    },
    {
      url: `${baseUrl}/games`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/categories`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/search`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.6,
    },
    {
      url: `${baseUrl}/pages/privacy`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.3,
    },
  ]

  // 获取游戏页面
  const gamePages: MetadataRoute.Sitemap = []

  try {
    // 获取热门游戏和最新游戏
    const [popularResult, latestResult] = await Promise.all([
      getPopularGames(100),
      getLatestGames(100)
    ])

    const gameUrls = new Set<string>()

    // 添加热门游戏
    if (popularResult.status === 'success' && popularResult.data) {
      popularResult.data.forEach(game => {
        if (game.game_url) {
          gameUrls.add(game.game_url)
        }
      })
    }

    // 添加最新游戏
    if (latestResult.status === 'success' && latestResult.data) {
      latestResult.data.forEach(game => {
        if (game.game_url) {
          gameUrls.add(game.game_url)
        }
      })
    }

    // 确保包含主要游戏
    gameUrls.add('drive-mad')

    // 生成游戏页面sitemap条目
    gameUrls.forEach(gameUrl => {
      gamePages.push({
        url: `${baseUrl}/games/${gameUrl}`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.8,
      })
    })
  } catch (error) {
    console.error('Error generating game sitemap entries:', error)
    // 添加基本游戏页面作为备用
    gamePages.push({
      url: `${baseUrl}/games/drive-mad`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.8,
    })
  }

  // 获取分类页面
  const categoryPages: MetadataRoute.Sitemap = []

  try {
    const categoriesResult = await getCategories()

    if (categoriesResult.status === 'success' && categoriesResult.data) {
      categoriesResult.data.forEach(category => {
        if (category.category_pilot) {
          categoryPages.push({
            url: `${baseUrl}/categories/${category.category_pilot}`,
            lastModified: new Date(),
            changeFrequency: 'weekly',
            priority: 0.7,
          })
        }
      })
    }
  } catch (error) {
    console.error('Error generating category sitemap entries:', error)
  }

  return [...staticPages, ...gamePages, ...categoryPages]
}
