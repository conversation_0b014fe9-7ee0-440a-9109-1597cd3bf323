import { getGameDetails } from "@/lib/apiService";
import { Metadata } from "next";

export default function GameLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div>
      {children}
    </div>
  );
}

interface GameParams {
  params: {
    gameName: string;
  };
}

export async function generateMetadata({ params }: GameParams): Promise<Metadata> {
  // 确保先解析params参数
  const resolvedParams = await Promise.resolve(params);
  const gameName = resolvedParams.gameName;

  // 获取游戏详情数据
  let gameData;
  try {
    if (!gameName) {
      throw new Error("Game name is required");
    }

    const result = await getGameDetails(gameName);
    if (result.status === 'success' && result.data) {
      gameData = result.data;
    }
  } catch (error) {
    console.error('Error fetching game data for metadata:', error);
  }

  // 如果成功获取到游戏数据，则使用它来生成元数据
  if (gameData) {
    const title = `${gameData.name} - Play Free Online | Drive Mad`;
    let description = gameData.description || `Play ${gameData.name} online for free on Drive Mad. Enjoy this fun ${gameData.category_name || 'online'} game!`;

    // 去除HTML标签
    description = description.replace(/<\/?[^>]+(>|$)/g, "");

    // 限制描述长度
    if (description.length > 160) {
      description = description.substring(0, 157) + '...';
    }

    // 生成相关关键词
    const category = gameData.category_name || 'online';
    const keywordsList = [
      `${gameData.name} game`,
      `play ${gameData.name}`,
      `${gameData.name} online`,
      `free ${gameData.name}`,
      `${category} games`,
      `drive mad ${category}`,
      `physics-based games`,
      `online driving games`,
      `browser games`,
      `addictive games`
    ];

    // 确保图片URL是完整的
    const imageUrl = gameData.image ?
      (gameData.image.startsWith('http') ? gameData.image : `https://drivemad.store${gameData.image}`) :
      'https://drivemad.store/images/og-image.jpg';

    return {
      title,
      description,
      keywords: keywordsList.join(', '),
      authors: [{ name: 'Drive Mad' }],
      creator: 'Drive Mad',
      publisher: 'Drive Mad',
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
      openGraph: {
        title: `${gameData.name} - Drive Mad`,
        description,
        images: [{
          url: imageUrl,
          alt: gameData.name,
          width: 1200,
          height: 630
        }],
        type: 'website',
        url: `https://drivemad.store/games/${gameName}`,
        siteName: 'Drive Mad',
        locale: 'en_US',
      },
      twitter: {
        card: 'summary_large_image',
        title: `Play ${gameData.name} on Drive Mad`,
        description,
        images: [imageUrl],
        creator: '@drivemad',
        site: '@drivemad',
      },
      alternates: {
        canonical: `https://drivemad.store/games/${gameName}`,
      },
      other: {
        'game:name': gameData.name,
        'game:category': gameData.category_name || 'Action',
        'game:platform': 'Web Browser',
        'game:price': 'Free',
      }
    };
  }

  // 默认元数据
  return {
    title: 'Play Online Games | Drive Mad',
    description: 'Play free online games at Drive Mad! Enjoy a wide selection of games including physics-based driving, racing, puzzle and more.',
    keywords: 'online games, free games, physics games, driving games, browser games',
  };
}