import { NextResponse } from 'next/server';

// 静态导出配置
export const dynamic = 'force-static';
export const revalidate = 3600; // 重验证间隔为1小时

// API基础URL
const API_BASE_URL = "https://api.drivemad.store";

// 默认游戏数据，当API不可用时使用
const DEFAULT_GAMES = [
  // 可以在这里添加默认游戏数据
];

// 在静态导出模式下，我们不能使用request.url
// 所以我们预先为几个常用的limit值生成静态数据
export async function GET() {
  // 静态导出模式下使用固定的limit值
  const limit = '10';
  
  try {
    console.log(`API Route: Fetching popular games with limit ${limit}...`);
    
    // 尝试不同的API端点
    const urls = [
      `${API_BASE_URL}/games/popular?limit=${limit}&appname=drivemadstore`,
      `${API_BASE_URL}/api/popular-games?limit=${limit}&appname=drivemadstore`
    ];
    
    let data = null;
    
    for (const url of urls) {
      try {
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
          },
          next: { revalidate }
        });

        if (response.ok) {
          data = await response.json();
          if (data && (data.data || data.games)) {
            console.log(`API Route: Successfully fetched popular games from ${url}`);
            break;
          }
        }
      } catch (e) {
        console.error(`Failed to fetch from ${url}:`, e);
      }
    }
    
    if (data) {
      return NextResponse.json(data);
    }
    
    throw new Error('All API endpoints failed');
  } catch (error) {
    console.error('API Route: Error proxying popular games request:', error);
    
    // 详细记录错误以便调试
    if (error instanceof Error) {
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });
    }
    
    // 出错时返回默认数据或空数据
    return NextResponse.json({
      status: "success",
      data: DEFAULT_GAMES.slice(0, parseInt(limit)),
      total: DEFAULT_GAMES.length
    });
  }
} 