import { Game } from "@/lib/apiService";

interface StructuredDataProps {
  game: Game;
  gameName: string;
}

export function GameStructuredData({ game, gameName }: StructuredDataProps) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "VideoGame",
    "name": game.name || gameName,
    "description": game.description || `Play ${game.name || gameName} online for free`,
    "image": game.image ? (game.image.startsWith('http') ? game.image : `https://drivemad.store${game.image}`) : undefined,
    "url": `https://drivemad.store/games/${gameName}`,
    "genre": game.tags ? game.tags.split(',').map(tag => tag.trim()) : ["Action", "Driving"],
    "gamePlatform": "Web Browser",
    "operatingSystem": "Any",
    "applicationCategory": "Game",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Drive Mad",
      "url": "https://drivemad.store"
    },
    "datePublished": game.created_at || new Date().toISOString(),
    "interactionStatistic": [
      {
        "@type": "InteractionCounter",
        "interactionType": "https://schema.org/LikeAction",
        "userInteractionCount": game.like_count || 0
      },
      {
        "@type": "InteractionCounter", 
        "interactionType": "https://schema.org/PlayAction",
        "userInteractionCount": game.play_count || 0
      }
    ]
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData, null, 2)
      }}
    />
  );
}

interface BreadcrumbStructuredDataProps {
  gameName: string;
  gameTitle: string;
}

export function BreadcrumbStructuredData({ gameName, gameTitle }: BreadcrumbStructuredDataProps) {
  const breadcrumbData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": "https://drivemad.store"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Games",
        "item": "https://drivemad.store/games"
      },
      {
        "@type": "ListItem",
        "position": 3,
        "name": gameTitle,
        "item": `https://drivemad.store/games/${gameName}`
      }
    ]
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(breadcrumbData, null, 2)
      }}
    />
  );
}
