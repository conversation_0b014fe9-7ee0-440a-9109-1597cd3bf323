"use client";

import { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';

// 定义页面内容的接口
interface PageContent {
  title: string;
  content: string;
  sections: {
    title: string;
    content: string;
  }[];
}

interface CMSPageContentProps {
  pageContent: PageContent;
  error: string | null;
}

export default function CMSPageContent({ pageContent, error }: CMSPageContentProps) {
  // 检查页面内容是否为空
  const isEmpty = !pageContent.title && !pageContent.content && (!pageContent.sections || pageContent.sections.length === 0);
  
  // 获取当前路径
  const pathname = usePathname();
  const isContactPage = pathname === '/pages/contact';
  
  // 联系表单状态
  const [name, setName] = useState('');
  const [email, setEmail] = useState('<EMAIL>'); // 预填充邮箱
  const [message, setMessage] = useState('');
  
  // 处理表单提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // 构建mailto链接
    const subject = `联系表单: ${name}`;
    const body = `姓名: ${name}\n邮箱: ${email}\n\n消息内容:\n${message}`;
    
    // 创建mailto URL并打开
    const mailtoUrl = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.location.href = mailtoUrl;
  };
  
  // 渲染联系表单
  if (isContactPage) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 md:p-8">
        <h1 className="text-xl md:text-2xl font-bold text-gray-900 mb-4">Contact Us</h1>
        
        <div className="mb-6">
          <p className="text-gray-700">
            Have any questions or feedback? We'd love to hear from you! You can reach
            us at the following:
          </p>
        </div>
        
        <div className="mb-6">
          <p className="text-gray-700">
            <strong>Email:</strong> <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a>
          </p>
          
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="name" className="block text-gray-700 font-medium mb-1">Name:</label>
            <input
              type="text"
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white"
              required
            />
          </div>
          
          <div>
            <label htmlFor="email" className="block text-gray-700 font-medium mb-1">Email:</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-gray-100"
              required
            />
          </div>
          
          <div>
            <label htmlFor="message" className="block text-gray-700 font-medium mb-1">Message:</label>
            <textarea
              id="message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 h-32 text-gray-900 bg-white"
              required
            />
          </div>
          
          <button
            type="submit"
            className="w-full bg-gray-800 hover:bg-gray-700 text-white font-medium py-3 px-4 rounded-md transition-colors"
          >
            Send Message
          </button>
        </form>
      </div>
    );
  }
  
  // 其他页面内容的正常渲染
  return (
    <div className="bg-white rounded-lg shadow-md p-6 md:p-8">
      {error ? (
        // Error message
        <div className="text-center py-12">
          <svg className="mx-auto h-12 w-12 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <h2 className="mt-4 text-lg font-medium text-red-800">{error}</h2>
        </div>
      ) : isEmpty ? (
        // No content message
        <div className="text-center py-12">
          <svg className="mx-auto h-12 w-12 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h2 className="mt-4 text-lg font-medium text-gray-800">Content is being prepared. Please check back later.</h2>
        </div>
      ) : (
        // Page content
        <article className="prose prose-blue max-w-none">
          {pageContent.title && (
            <h1 className="text-xl md:text-2xl font-bold text-gray-900 mb-4" 
                dangerouslySetInnerHTML={{ __html: pageContent.title }} />
          )}
          
          {pageContent.content && (
            <div className="text-gray-700 mb-6 leading-relaxed"
                 dangerouslySetInnerHTML={{ __html: pageContent.content }} />
          )}
          
          {pageContent.sections && pageContent.sections.map((section, index) => (
            <section key={index} className="mt-8">
              {section.title && (
                <h2 className="text-lg md:text-xl font-semibold text-gray-800 mb-3"
                    dangerouslySetInnerHTML={{ __html: section.title }} />
              )}
              {section.content && (
                <div className="text-gray-700 leading-relaxed"
                     dangerouslySetInnerHTML={{ __html: section.content }} />
              )}
            </section>
          ))}
        </article>
      )}
    </div>
  );
} 