import { Metadata } from "next";

export default function CategoryLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>{children}</>
  );
}

interface CategoryParams {
  params: {
    categorySlug: string;
  };
}

export async function generateMetadata({ params }: CategoryParams): Promise<Metadata> {
  // 从URL中获取分类名称，在Next.js 15中需要使用await获取params属性
  const categorySlug = (await params).categorySlug;
  
  // 将slug格式化为可读名称
  const formattedName = formatCategoryName(categorySlug);
  
  return {
    title: `${formattedName} Games - Play the Best Online Games | Drive Mad`,
    description: `Play the best ${formattedName.toLowerCase()} games online at Drive Mad. Explore our collection of free ${formattedName.toLowerCase()} games with challenging physics-based driving puzzles and exciting adventures.`,
    keywords: `${formattedName.toLowerCase()} games, drive mad ${formattedName.toLowerCase()}, online ${formattedName.toLowerCase()} games, free ${formattedName.toLowerCase()} games, physics games`,
    openGraph: {
      title: `${formattedName} Games - Drive Mad`,
      description: `Explore our collection of free ${formattedName.toLowerCase()} games. Challenging physics-based driving puzzles and exciting adventures.`,
      type: 'website',
      url: `/categories/${categorySlug}`,
    },
  };
}

// 辅助函数：将slug格式化为可读的分类名称
function formatCategoryName(slug: string): string {
  return slug
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
} 