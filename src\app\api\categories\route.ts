import { NextResponse } from 'next/server';
import { processImagePath } from '@/lib/apiService';

// 为静态导出配置API路由
export const dynamic = 'force-static';
export const revalidate = 3600; // 每小时重新验证一次

// 修复图片路径，将 /cat/ 替换为 /images/cat/
function fixCategoryImages(categories: any[]) {
  return categories.map(category => {
    // 使用全局processImagePath函数处理图片路径
    if (category.image) {
      category.image = processImagePath(category.image, category.category_pilot);
    } else {
      // 没有图片的情况，使用默认图片
      const categorySlug = category.category_pilot || 
                         category.slug || 
                         category.name?.toLowerCase().replace(/\s+/g, '-');
      
      if (categorySlug) {
        category.image = processImagePath('', categorySlug);
      }
    }
    return category;
  });
}

export async function GET() {
  console.log('API Route: Fetching categories...');
  
  const API_BASE_URL = "https://api.drivemad.store";
  const possibleUrls = [
    `${API_BASE_URL}/categories?appname=drivemadstore`,
    `${API_BASE_URL}/api/categories?appname=drivemadstore`,
  ];
  
  // 尝试所有可能的URL
  for (const url of possibleUrls) {
    try {
      console.log(`API Route: Trying to fetch categories from ${url}`);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        },
        next: { revalidate: 3600 } // 缓存1小时
      });

      if (!response.ok) {
        console.error(`API Route: External API returned status ${response.status} for ${url}`);
        continue; // 尝试下一个URL
      }

      const data = await response.json();
      if (data && data.data && Array.isArray(data.data)) {
        console.log(`API Route: Successfully fetched ${data.data.length} categories from ${url}`);
        
        // 修复图片路径
        data.data = fixCategoryImages(data.data);
        console.log('API Route: Fixed category image paths');
        
        return NextResponse.json(data);
      }
      
      console.error(`API Route: Invalid data format from ${url}`);
    } catch (error) {
      console.error(`API Route: Error fetching categories from ${url}:`, error);
    }
  }

  // 所有URL都失败，返回错误响应
  console.log('API Route: All category fetch attempts failed');
  return NextResponse.json({
    status: "error",
    message: "Failed to fetch categories from all available API endpoints",
    data: []
  }, { status: 500 });
} 