import { getGameDetails, getPopularGames, getLatestGames } from "@/lib/apiService";
import GamePlayClient from "./GamePlayClient";

interface GameParams {
  params: Promise<{
    gameName: string;
  }>;
}

// 生成静态参数用于预渲染游戏播放页面
export async function generateStaticParams() {
  try {
    // 获取热门游戏和最新游戏的列表
    const [popularResult, latestResult] = await Promise.all([
      getPopularGames(50), // 获取前50个热门游戏
      getLatestGames(50)   // 获取前50个最新游戏
    ]);

    const gameNames = new Set<string>();

    // 添加热门游戏
    if (popularResult.status === 'success' && popularResult.data) {
      popularResult.data.forEach(game => {
        if (game.game_url) {
          gameNames.add(game.game_url);
        }
      });
    }

    // 添加最新游戏
    if (latestResult.status === 'success' && latestResult.data) {
      latestResult.data.forEach(game => {
        if (game.game_url) {
          gameNames.add(game.game_url);
        }
      });
    }

    // 确保包含主要游戏
    gameNames.add('drive-mad');

    // 转换为参数格式
    return Array.from(gameNames).map((gameName) => ({
      gameName: gameName,
    }));
  } catch (error) {
    console.error('Error generating static params for game play pages:', error);
    // 返回基本的游戏列表作为备用
    return [
      { gameName: 'drive-mad' },
    ];
  }
}

export default async function GamePlayPage({ params }: GameParams) {
  // 确保先解析params参数
  const resolvedParams = await params;
  const gameName = resolvedParams.gameName;

  // 在服务端获取游戏数据
  let initialData = null;

  try {
    const gameResult = await getGameDetails(gameName);
    if (gameResult.status === 'success') {
      initialData = gameResult.data;
    }
  } catch (error) {
    console.error("Error fetching game details on server:", error);
  }

  return (
    <GamePlayClient
      gameName={gameName}
      initialData={initialData}
    />
  );
}