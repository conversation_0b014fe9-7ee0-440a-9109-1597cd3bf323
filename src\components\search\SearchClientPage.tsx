"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import SearchResults from "./SearchResults";
import { Game } from "@/lib/apiService";

export default function SearchClientPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [query, setQuery] = useState("");
  const [initialResults, setInitialResults] = useState<Game[]>([]);
  const [initialTotal, setInitialTotal] = useState(0);
  const [searchInput, setSearchInput] = useState("");

  useEffect(() => {
    // 从URL获取查询参数
    const q = searchParams.get("q") || "";
    setQuery(q);
    setSearchInput(q);
  }, [searchParams]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchInput.trim()) {
      // 使用客户端导航更新URL
      router.push(`/search?q=${encodeURIComponent(searchInput.trim())}`);
    }
  };

  return (
    <div>
      {query ? (
        <SearchResults
          initialQuery={query}
          initialResults={initialResults}
          initialTotal={initialTotal}
        />
      ) : (
        <div className="relative mb-8 bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg overflow-hidden">
          <div className="absolute inset-0 opacity-10 bg-pattern"></div>
          <div className="relative z-10 px-6 py-8 md:px-10 text-white">
            <h2 className="text-2xl md:text-3xl font-bold mb-4">Search Games</h2>
            <p className="text-blue-100 mb-6">Find your favorite games by name, category, or keywords</p>
            
            <form onSubmit={handleSearch} className="relative max-w-xl">
              <input
                type="text"
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                placeholder="Enter game name or category..."
                className="w-full px-4 py-3 pr-14 rounded-lg border-0 focus:ring-2 focus:ring-blue-300 text-gray-700 shadow-md"
              />
              <button
                type="submit"
                className="absolute right-2 top-1/2 -translate-y-1/2 bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-md transition-colors"
                aria-label="Search"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
              </button>
            </form>
            
            <div className="mt-8 grid grid-cols-2 sm:grid-cols-4 gap-3">
              <button 
                onClick={() => {
                  setSearchInput("Adventure");
                  router.push(`/search?q=${encodeURIComponent("Adventure")}`);
                }}
                className="bg-white/10 hover:bg-white/20 border border-white/20 text-white px-4 py-2 rounded-lg transition-colors shadow-sm text-sm font-medium"
              >
                Adventure Games
              </button>
              <button 
                onClick={() => {
                  setSearchInput("Puzzle");
                  router.push(`/search?q=${encodeURIComponent("Puzzle")}`);
                }}
                className="bg-white/10 hover:bg-white/20 border border-white/20 text-white px-4 py-2 rounded-lg transition-colors shadow-sm text-sm font-medium"
              >
                Puzzle Games
              </button>
              <button 
                onClick={() => {
                  setSearchInput("Multiplayer");
                  router.push(`/search?q=${encodeURIComponent("Multiplayer")}`);
                }}
                className="bg-white/10 hover:bg-white/20 border border-white/20 text-white px-4 py-2 rounded-lg transition-colors shadow-sm text-sm font-medium"
              >
                Multiplayer Games
              </button>
              <button 
                onClick={() => {
                  setSearchInput("Racing");
                  router.push(`/search?q=${encodeURIComponent("Racing")}`);
                }}
                className="bg-white/10 hover:bg-white/20 border border-white/20 text-white px-4 py-2 rounded-lg transition-colors shadow-sm text-sm font-medium"
              >
                Racing Games
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 