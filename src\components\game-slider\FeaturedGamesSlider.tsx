"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { normalizeImagePath } from "@/lib/apiService";

interface FeaturedGame {
  id: string;
  title: string;
  imageUrl: string;
  category: string;
}

interface FeaturedGamesSliderProps {
  games: FeaturedGame[];
}

export function FeaturedGamesSlider({ games }: FeaturedGamesSliderProps) {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % games.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [games.length]);

  const handleDotClick = (index: number) => {
    setCurrentIndex(index);
  };

  const nextSlide = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % games.length);
  };

  const prevSlide = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + games.length) % games.length);
  };

  return (
    <div className="relative h-[300px] md:h-[400px]">
      {/* 轮播内容 */}
      <div className="absolute inset-0 overflow-hidden rounded-lg shadow-md">
        {games.map((game, index) => (
          <div
            key={game.id}
            className={`absolute inset-0 transition-opacity duration-500 ${
              index === currentIndex ? "opacity-100" : "opacity-0 pointer-events-none"
            }`}
          >
            <div className="w-full h-full relative">
              <Image
                src={normalizeImagePath(game.imageUrl)}
                alt={game.title}
                fill
                className="w-full h-full object-cover"
                priority={index === currentIndex}
              />
              {/* 移动端和桌面端的底部渐变遮罩 */}
              <div className="absolute inset-x-0 bottom-0 h-24 bg-gradient-to-t from-black/80 to-transparent"></div>
              
              {/* 游戏信息区域 */}
              <div className="absolute inset-x-0 bottom-0 p-4 flex flex-col md:flex-row justify-between items-end md:items-center">
                <div className="mb-2 md:mb-0">
                  <h3 className="text-white font-bold text-xl md:text-2xl">{game.title}</h3>
                  <div className="hidden md:flex items-center mt-1">
                    <span className="text-white/90 text-xs md:text-sm bg-blue-600/80 px-2 py-0.5 rounded-sm">
                      {game.category}
                    </span>
                  </div>
                </div>
                
                {/* 缩略图和按钮 */}
                <div className="flex items-center space-x-2">
                  <div className="hidden sm:flex space-x-2">
                    {games.map((thumbGame, thumbIndex) => (
                      <button
                        key={`quick-thumb-${thumbGame.id}`}
                        onClick={() => handleDotClick(thumbIndex)}
                        className={`w-12 h-8 rounded overflow-hidden border-2 transition-all ${
                          thumbIndex === currentIndex ? "border-orange-500" : "border-white/30"
                        } focus:outline-none`}
                      >
                        <div className="w-full h-full relative">
                          <Image
                            src={normalizeImagePath(thumbGame.imageUrl)}
                            alt={`Thumbnail for ${thumbGame.title}`}
                            width={48}
                            height={32}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      </button>
                    ))}
                  </div>
                  <Link href={`/games/${game.id}`}>
                    <button className="play-now-button whitespace-nowrap flex items-center">
                      <span className="mr-1">PLAY NOW</span>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 移动端指示点 */}
      <div className="absolute bottom-16 left-1/2 transform -translate-x-1/2 flex space-x-2 z-10 sm:hidden">
        {games.map((_, index) => (
          <button
            key={`dot-${index}`}
            onClick={() => handleDotClick(index)}
            className={`w-2 h-2 rounded-full ${
              index === currentIndex ? "bg-orange-500" : "bg-white/40"
            } focus:outline-none transition-colors`}
            aria-label={`Go to slide ${index + 1}`}
          >
          </button>
        ))}
      </div>

      {/* 箭头导航 */}
      <button
        onClick={prevSlide}
        className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/30 text-white p-2 rounded-full hover:bg-black/50 transition-colors z-10 focus:outline-none"
        aria-label="Previous slide"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 19l-7-7 7-7"
          />
        </svg>
      </button>
      <button
        onClick={nextSlide}
        className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/30 text-white p-2 rounded-full hover:bg-black/50 transition-colors z-10 focus:outline-none"
        aria-label="Next slide"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 5l7 7-7 7"
          />
        </svg>
      </button>
    </div>
  );
}
