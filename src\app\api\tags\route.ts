import { NextResponse } from 'next/server';
import { DEFAULT_TAGS } from '@/lib/apiService';

// 为静态导出配置API路由
export const dynamic = 'force-static';
export const revalidate = 3600; // 每小时重新验证一次

export async function GET() {
  console.log('API Route: Fetching tags...');
  
  const API_BASE_URL = "https://api.drivemad.store";
  const possibleUrls = [
    `${API_BASE_URL}/tags?appname=drivemadstore`,
    `${API_BASE_URL}/api/tags?appname=drivemadstore`,
  ];
  
  // 尝试所有可能的URL
  for (const url of possibleUrls) {
    try {
      console.log(`API Route: Trying to fetch tags from ${url}`);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        },
        next: { revalidate: 3600 } // 缓存1小时
      });

      if (!response.ok) {
        console.error(`API Route: External API returned status ${response.status} for ${url}`);
        continue; // 尝试下一个URL
      }

      const data = await response.json();
      if (data && data.data && Array.isArray(data.data)) {
        console.log(`API Route: Successfully fetched ${data.data.length} tags from ${url}`);
        return NextResponse.json(data);
      }
      
      console.error(`API Route: Invalid data format from ${url}`);
    } catch (error) {
      console.error(`API Route: Error fetching tags from ${url}:`, error);
    }
  }

  // 所有URL都失败，返回默认数据
  console.log('API Route: All tag fetch attempts failed, returning default tags');
  return NextResponse.json({
    status: "success",
    data: DEFAULT_TAGS
  });
} 