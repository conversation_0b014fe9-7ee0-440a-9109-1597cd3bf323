import { getGamesByCategory, Game, normalizeImagePath, getCategories } from "@/lib/apiService";
import Link from "next/link";
import Image from "next/image";
import { notFound } from "next/navigation";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";

// 游戏数据页面组件
interface CategoryPageProps {
  params: Promise<{ categorySlug: string }>;
}

// 生成静态参数用于预渲染分类页面
export async function generateStaticParams() {
  try {
    // 获取所有分类
    const categoriesResult = await getCategories();

    if (categoriesResult.status === 'success' && categoriesResult.data) {
      // 转换为参数格式
      return categoriesResult.data.map((category: any) => ({
        categorySlug: category.category_pilot || category.slug || category.name.toLowerCase().replace(/\s+/g, '-'),
      }));
    }

    // 如果获取分类失败，返回一些常见的分类作为备用
    return [
      { categorySlug: 'action-games' },
      { categorySlug: 'adventure-games' },
      { categorySlug: 'puzzle-games' },
      { categorySlug: 'racing-games' },
      { categorySlug: 'sports-games' },
      { categorySlug: 'strategy-games' },
      { categorySlug: 'arcade-games' },
      { categorySlug: 'casual-games' },
    ];
  } catch (error) {
    console.error('Error generating static params for category pages:', error);
    // 返回基本的分类列表作为备用
    return [
      { categorySlug: 'action-games' },
      { categorySlug: 'adventure-games' },
      { categorySlug: 'puzzle-games' },
      { categorySlug: 'racing-games' },
      { categorySlug: 'sports-games' },
      { categorySlug: 'strategy-games' },
      { categorySlug: 'arcade-games' },
      { categorySlug: 'casual-games' },
    ];
  }
}

// 每页游戏数量
const GAMES_PER_PAGE = 20;

// 格式化分类名称
function formatCategoryName(slug: string): string {
  return slug
    .charAt(0).toUpperCase() +
    slug.slice(1)
      .replace(/-/g, ' ')
      .replace(/games$/, ' Games');
}

// 服务器端获取游戏数据
async function fetchGames(categorySlug: string, pageNum: number) {
  try {
    // 计算分页偏移量
    const offset = (pageNum - 1) * GAMES_PER_PAGE;

    console.log(`[SERVER] Fetching games for category: ${categorySlug}, page: ${pageNum}, offset: ${offset}`);

    // 从API获取数据
    const result = await getGamesByCategory(categorySlug, GAMES_PER_PAGE, offset);
    console.log("[SERVER] API result:", result);

    if (result.status === 'success' && result.data && result.data.length > 0) {
      // API成功返回数据
      console.log(`[SERVER] Successfully loaded ${result.data.length} games from API`);

      // 计算总游戏数和总页数
      const totalGames = result.total || result.data.length;
      const totalPages = Math.ceil(totalGames / GAMES_PER_PAGE);

      // 提取分类名称，如果第一个游戏有分类，使用它；否则使用格式化的slug
      const categoryName = result.data[0].category_name || formatCategoryName(categorySlug);

      return {
        games: result.data,
        totalGames,
        totalPages,
        categoryName,
        error: null
      };
    } else {
      // API没有返回游戏数据
      console.log(`[SERVER] No games found from API for category: ${categorySlug}`);

      // 尝试对racing-games等特定分类使用替代方法
      if (categorySlug === 'racing-games') {
        console.log("[SERVER] Trying alternative API path for racing games...");
        const racingResult = await getGamesByCategory('racing', GAMES_PER_PAGE, offset);

        if (racingResult.status === 'success' && racingResult.data && racingResult.data.length > 0) {
          console.log(`[SERVER] Found ${racingResult.data.length} racing games with alternative path`);

          const totalGames = racingResult.total || racingResult.data.length;
          const totalPages = Math.ceil(totalGames / GAMES_PER_PAGE);

          return {
            games: racingResult.data,
            totalGames,
            totalPages,
            categoryName: "Racing Games",
            error: null
          };
        }
      }

      // 如果所有尝试都失败，返回错误消息
      return {
        games: [],
        totalGames: 0,
        totalPages: 1,
        categoryName: formatCategoryName(categorySlug),
        error: `No games found in this category: ${formatCategoryName(categorySlug)}`
      };
    }
  } catch (error) {
    console.error("[SERVER] Error fetching category games:", error);
    return {
      games: [],
      totalGames: 0,
      totalPages: 1,
      categoryName: formatCategoryName(categorySlug),
      error: "Failed to load games. Please try again later."
    };
  }
}

// 生成分页链接
function generatePaginationLinks(currentPage: number, totalPages: number) {
  const pageLinks = [];

  // 总是显示第一页
  pageLinks.push(1);

  // 显示当前页附近的页码
  let start = Math.max(2, currentPage - 1);
  let end = Math.min(totalPages - 1, currentPage + 1);

  // 如果起始页不是2，添加省略号
  if (start > 2) {
    pageLinks.push('...');
  }

  // 添加中间的页码
  for (let i = start; i <= end; i++) {
    pageLinks.push(i);
  }

  // 如果结束页不是倒数第二页，添加省略号
  if (end < totalPages - 1) {
    pageLinks.push('...');
  }

  // 如果总页数大于1，总是显示最后一页
  if (totalPages > 1) {
    pageLinks.push(totalPages);
  }

  return pageLinks;
}

// 分类页面组件
export default async function CategoryPage({ params }: CategoryPageProps) {
  // 在Next.js 15中，需要使用await获取params属性
  const resolvedParams = await params;
  const categorySlug = resolvedParams.categorySlug;

  // 在静态导出模式下，只获取第一页的数据
  const currentPage = 1;

  // 从服务器获取游戏数据
  const { games, totalGames, totalPages, categoryName, error } =
    await fetchGames(categorySlug, currentPage);

  // 创建排序后的游戏列表（按热门程度排序）
  const sortedGames = [...games].sort((a, b) => (b.plays || 0) - (a.plays || 0));

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <Header />
      <main className="flex-grow container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          {/* 分类头部 */}
          <div className="relative mb-10 bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg overflow-hidden">
            <div className="absolute inset-0 opacity-10 bg-pattern"></div>
            <div className="relative z-10 px-6 py-10 md:px-10 text-white">
              <div className="flex flex-col md:flex-row md:items-center justify-between">
                <div>
                  <div className="flex items-center mb-4">
                    <Link href="/categories" className="text-blue-200 hover:text-white transition-colors mr-2 flex items-center">
                      <svg
                        className="w-4 h-4 mr-1"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                      </svg>
                      Categories
                    </Link>
                    <span className="text-blue-300 mx-1">/</span>
                    <h1 className="text-2xl md:text-3xl font-bold">{categoryName}</h1>
                  </div>
                  <p className="text-blue-100 max-w-2xl">
                    Explore our collection of {totalGames || 'many'} {categoryName.toLowerCase()} games. Play for free on any device right in your browser.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {error ? (
            <div className="text-center py-16 bg-white rounded-xl shadow-md p-8 max-w-2xl mx-auto">
              <div className="bg-red-50 p-6 rounded-lg mb-6">
                <svg className="w-16 h-16 text-red-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h2 className="text-2xl font-bold text-red-600 mb-2">No Games Found</h2>
                <p className="text-gray-600 mb-6">{error}</p>
              </div>
              <Link
                href="/categories"
                className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-8 rounded-lg transition-colors inline-flex items-center group"
              >
                <svg className="w-5 h-5 mr-2 group-hover:animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16l-4-4m0 0l4-4m-4 4h18" />
                </svg>
                Browse All Categories
              </Link>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 md:gap-6">
                {sortedGames.map((game) => (
                  <Link key={game.game_id || game.game_name} href={`/games/${game.game_name}`}>
                    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:scale-105 h-full flex flex-col">
                      <div className="relative aspect-[4/3] overflow-hidden">
                        <Image
                          src={normalizeImagePath(game.image)}
                          alt={game.name}
                          fill
                          className="object-cover hover:scale-110 transition-transform duration-500"
                          sizes="(max-width: 640px) 45vw, (max-width: 768px) 30vw, (max-width: 1024px) 25vw, 20vw"
                        />
                        {game.rating && (
                          <div className="absolute top-2 right-2 bg-green-600 text-white text-xs font-medium px-1.5 md:px-2 py-0.5 md:py-1 rounded-md flex items-center">
                            <svg className="w-3 h-3 mr-0.5 text-yellow-300" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            {game.rating}
                          </div>
                        )}
                      </div>
                      <div className="p-3 md:p-4 flex-grow flex flex-col">
                        <h3 className="font-semibold text-gray-900 mb-1 truncate text-sm md:text-base">{game.name}</h3>
                        <div className="text-xs mb-2 flex-grow hidden sm:block">
                          {game.tags_name ? (
                            <div className="flex flex-wrap gap-1">
                              {game.tags_name.split(',').map((tag, index) => (
                                <span key={index} className="bg-blue-50 text-blue-600 px-2 py-0.5 rounded-full text-xs">
                                  {tag.trim()}
                                </span>
                              )).slice(0, 2)}
                            </div>
                          ) : game.category_name ? (
                            <span className="bg-blue-50 text-blue-600 px-2 py-0.5 rounded-full text-xs">
                              {game.category_name}
                            </span>
                          ) : null}
                        </div>
                        <div className="flex items-center justify-between mt-auto">
                          <span className="text-blue-600 text-xs sm:text-sm font-medium inline-flex items-center group">
                            Play
                            <svg className="w-3 h-3 sm:w-4 sm:h-4 ml-1 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                            </svg>
                          </span>
                          <div className="bg-blue-50 text-blue-700 p-1 rounded-full">
                            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>

              {/* 显示更多游戏的提示 */}
              {totalGames > GAMES_PER_PAGE && (
                <div className="text-center mt-12 mb-8">
                  <div className="bg-white p-6 rounded-lg shadow-md">
                    <p className="text-gray-600 mb-4">
                      Showing {games.length} of {totalGames} {categoryName.toLowerCase()}
                    </p>
                    <p className="text-sm text-gray-500">
                      More games will be available in the full interactive version
                    </p>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
}