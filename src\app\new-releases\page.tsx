import { getLatestGames, Game } from "@/lib/apiService";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { GameCard } from "@/components/game-card/GameCard";
import Link from "next/link";
import { notFound } from "next/navigation";

// 注意：对于静态导出，页面将在构建时生成

// 游戏数据格式化
function formatGameData(gameData: Game) {
  return {
    id: gameData.game_name,
    title: gameData.name,
    imageUrl: gameData.image,
    category: gameData.category_name,
    plays: gameData.plays,
  };
}

// 服务器端获取数据
async function fetchGames(pageNum: number, itemsPerPage: number) {
  try {
    console.log(`[SERVER] Fetching latest games for page: ${pageNum}, items per page: ${itemsPerPage}`);

    // 计算偏移量，而不是直接使用pageNum作为偏移量
    // pageNum是从1开始的页码，而API需要的是从0开始的偏移量
    const offset = (pageNum - 1) * itemsPerPage;

    // 添加随机查询参数，避免缓存问题
    const timestamp = Date.now();
    console.log(`[SERVER] Using timestamp ${timestamp} to avoid caching`);

    // 传递正确的参数：itemsPerPage是限制数量，offset是偏移量
    const result = await getLatestGames(itemsPerPage, offset);

    if (result.status === 'success') {
      const formattedGames = result.data.map(formatGameData);
      console.log(`[SERVER] Received ${formattedGames.length} games of ${result.total || 'unknown'} total games`);

      // 计算总页数
      const totalPages = result.total
        ? Math.ceil(result.total / itemsPerPage)
        : (formattedGames.length > 0 ? Math.max(2, pageNum) : 1);

      return {
        games: formattedGames,
        totalPages,
        totalGames: result.total || formattedGames.length
      };
    }
    console.log('[SERVER] API returned no results or failed');
    return { games: [], totalPages: 1, totalGames: 0 };
  } catch (error) {
    console.error('[SERVER] Error fetching latest games:', error);
    return { games: [], totalPages: 1, totalGames: 0 };
  }
}

// 生成分页链接
function generatePaginationLinks(currentPage: number, totalPages: number) {
  const pageLinks = [];

  // 最多显示5个页码
  if (totalPages <= 5) {
    // 如果总页数小于等于5，显示所有页码
    for (let i = 1; i <= totalPages; i++) {
      pageLinks.push(i);
    }
  } else {
    // 总是显示第一页
    pageLinks.push(1);

    // 显示当前页附近的页码
    let start = Math.max(2, currentPage - 1);
    let end = Math.min(totalPages - 1, currentPage + 1);

    // 如果起始页不是2，添加省略号
    if (start > 2) {
      pageLinks.push('...');
    }

    // 添加中间的页码
    for (let i = start; i <= end; i++) {
      pageLinks.push(i);
    }

    // 如果结束页不是倒数第二页，添加省略号
    if (end < totalPages - 1) {
      pageLinks.push('...');
    }

    // 总是显示最后一页
    pageLinks.push(totalPages);
  }

  return pageLinks;
}

// 新发布游戏页面组件
export default async function NewReleasesPage() {
  // 对于静态导出，只生成第一页
  const currentPage = 1;
  const itemsPerPage = 24;

  // 获取当前页的游戏数据
  const { games, totalPages, totalGames } = await fetchGames(currentPage, itemsPerPage);

  // 如果请求的页码超出总页数且总页数不为0，跳转到404
  if (currentPage > totalPages && totalPages > 0) {
    notFound();
  }

  // 生成分页链接
  const paginationLinks = generatePaginationLinks(currentPage, totalPages);

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8 text-center">New Releases</h1>
        <p className="text-center text-white font-medium mb-8">
          Showing {games.length} of {totalGames || 'many'} latest games | Page {currentPage} of {totalPages}
        </p>

        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {games.length > 0 ? (
            games.map((game) => (
              <div key={`${currentPage}-${game.id}`} className="w-full h-full flex flex-col">
                <GameCard
                  id={game.id}
                  title={game.title}
                  imageUrl={game.imageUrl}
                  category={game.category}
                  plays={game.plays}
                  size="small"
                />
              </div>
            ))
          ) : (
            <div className="col-span-full text-center py-12">
              <h3 className="text-xl font-medium text-gray-600">No games found</h3>
              <p className="text-gray-500 mt-2">Check back later for new releases</p>
            </div>
          )}
        </div>

        {/* 注意：在静态导出模式下，分页功能被禁用 */}
        {games.length > 0 && totalPages > 1 && (
          <div className="mt-8 text-center">
            <p className="text-gray-600">
              Showing page 1 of {totalPages}. More games available in the full version.
            </p>
          </div>
        )}
      </main>
      <Footer />
    </div>
  );
}