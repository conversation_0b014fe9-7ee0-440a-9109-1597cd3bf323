"use client";

import { useState, useEffect } from 'react';
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { Game, getFeaturedGames } from "@/lib/apiService";
import Image from "next/image";
import Link from "next/link";

export default function FeaturedGamesPage() {
  const [games, setGames] = useState<Game[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  
  const gamesPerPage = 18; // Show 18 games per page
  const totalGames = 100; // Assuming total of 100 games (could be from API)
  const totalPages = Math.ceil(totalGames / gamesPerPage);

  useEffect(() => {
    async function loadFeaturedGames() {
      setIsLoading(true);
      setError(null);
      try {
        const result = await getFeaturedGames(48); // Load more games to enable pagination
        if (result.status === 'success') {
          setGames(result.data);
        } else {
          setError("Failed to load featured games");
        }
      } catch (err) {
        console.error("Error loading featured games:", err);
        setError("An error occurred while loading featured games");
      } finally {
        setIsLoading(false);
      }
    }

    loadFeaturedGames();
  }, []);

  // Calculate games for current page
  const indexOfLastGame = currentPage * gamesPerPage;
  const indexOfFirstGame = indexOfLastGame - gamesPerPage;
  const currentGames = games.slice(indexOfFirstGame, indexOfLastGame);

  // Change page
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  // Generate page numbers array
  const pageNumbers = [];
  for (let i = 1; i <= Math.min(Math.ceil(games.length / gamesPerPage), 10); i++) {
    pageNumbers.push(i);
  }

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <Header />
      <main className="flex-grow container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="bg-blue-600 text-white p-8 rounded-lg shadow-md mb-8">
          <h1 className="text-3xl font-bold mb-3">Featured Games</h1>
          <p className="text-blue-100 text-lg max-w-3xl">
            Explore our collection of hand-picked featured games for the best gaming experience. These games are selected for their quality, fun factor and popularity.
          </p>
        </div>

        {isLoading ? (
          // Loading state
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-5">
            {Array.from({ length: 18 }).map((_, index) => (
              <div key={index} className="bg-white rounded-lg overflow-hidden shadow-md animate-pulse">
                <div className="bg-blue-200" style={{ aspectRatio: "1/1" }}></div>
                <div className="p-3">
                  <div className="h-5 bg-gray-200 rounded mb-2"></div>
                  <div className="flex justify-between items-center">
                    <div className="h-4 w-16 bg-blue-100 rounded-full"></div>
                    <div className="h-4 w-12 bg-gray-200 rounded"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : error ? (
          // Error state
          <div className="text-center py-16 bg-red-50 rounded-lg shadow-inner">
            <svg className="w-16 h-16 text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <p className="text-red-600 mb-4 font-medium text-xl">{error}</p>
            <button 
              onClick={() => window.location.reload()}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium transition-colors"
            >
              Try Again
            </button>
          </div>
        ) : games.length === 0 ? (
          // Empty state
          <div className="text-center py-16 bg-gray-100 rounded-lg shadow-inner">
            <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <p className="text-gray-600 mb-2 font-medium text-xl">No featured games available at the moment</p>
            <p className="text-gray-500">Please check back later for new featured games</p>
          </div>
        ) : (
          // Game grid with pagination
          <>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-5 mb-8">
              {currentGames.map((game) => (
                <Link 
                  href={`/game/${game.game_name}`} 
                  key={game.game_id}
                  className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-all hover:-translate-y-1 group"
                >
                  <div className="relative bg-blue-100 overflow-hidden" style={{ aspectRatio: "1/1" }}>
                    <Image
                      src={game.image}
                      alt={game.name}
                      fill
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    {/* Rating badge */}
                    {game.rating && (
                      <div className="absolute top-2 right-2 bg-yellow-500 text-white text-xs font-bold px-2 py-1 rounded-full flex items-center shadow-md">
                        <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        {game.rating}
                      </div>
                    )}
                  </div>
                  <div className="p-3">
                    <h3 className="font-medium text-gray-800 truncate">{game.name}</h3>
                    <div className="flex justify-between items-center mt-2">
                      <span className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded-full truncate max-w-[100px]">{game.category_name}</span>
                      <span className="text-xs text-gray-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                          <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                        </svg>
                        {game.plays?.toLocaleString() || 0}
                      </span>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
            
            {/* Pagination */}
            {pageNumbers.length > 1 && (
              <div className="flex justify-center mt-8 mb-4">
                <nav className="flex items-center space-x-2">
                  <button
                    onClick={() => paginate(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="px-4 py-2 rounded-md bg-blue-600 text-white disabled:opacity-50 hover:bg-blue-700 font-medium text-sm transition-colors"
                  >
                    Previous
                  </button>
                  
                  <div className="flex space-x-2">
                    {pageNumbers.map(number => {
                      // Show first page, last page, current page, and pages around current
                      const showPageNumber = 
                        number === 1 || 
                        number === pageNumbers.length ||
                        (number >= currentPage - 1 && number <= currentPage + 1);
                      
                      // Show ellipsis for gaps
                      if (!showPageNumber) {
                        if (number === 2 || number === pageNumbers.length - 1) {
                          return (
                            <span key={number} className="w-8 h-8 flex items-center justify-center">
                              ...
                            </span>
                          );
                        }
                        return null;
                      }
                      
                      return (
                        <button
                          key={number}
                          onClick={() => paginate(number)}
                          className={`w-8 h-8 flex items-center justify-center rounded-md text-sm font-medium ${
                            number === currentPage 
                              ? 'bg-green-600 text-white' 
                              : 'bg-blue-600 text-white hover:bg-blue-700'
                          } transition-colors`}
                        >
                          {number}
                        </button>
                      );
                    })}
                  </div>
                  
                  <button
                    onClick={() => paginate(Math.min(pageNumbers.length, currentPage + 1))}
                    disabled={currentPage === pageNumbers.length}
                    className="px-4 py-2 rounded-md bg-blue-600 text-white disabled:opacity-50 hover:bg-blue-700 font-medium text-sm transition-colors"
                  >
                    Next
                  </button>
                </nav>
              </div>
            )}
          </>
        )}
      </main>
      <Footer />
    </div>
  );
} 