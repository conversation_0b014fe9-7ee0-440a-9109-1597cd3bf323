@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 100% 45%;
    --foreground: 0 0% 100%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 210 100% 45%;  /* kizi 蓝色 */
    --primary-foreground: 0 0% 100%;
    --secondary: 120 61% 34%; /* kizi 绿色 */
    --secondary-foreground: 0 0% 100%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;

    /* Kizi specific colors */
    --kizi-blue: 210 100% 45%;
    --kizi-light-blue: 205 80% 55%;
    --kizi-green: 120 61% 34%;
    --kizi-yellow: 48 95% 53%;
    --kizi-orange: 24 91% 54%;
    --kizi-red: 0 100% 50%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .nav-link {
    @apply inline-flex items-center text-white font-semibold text-sm px-3 py-2 rounded-md hover:bg-white/10 transition-colors;
  }

  .nav-button {
    @apply rounded-full text-white text-sm font-medium px-3 py-1.5 transition-colors;
  }

  .play-now-button {
    @apply bg-orange-500 hover:bg-orange-600 text-white font-bold text-sm px-4 py-2 rounded-md transition-all shadow-md hover:shadow-lg hover:-translate-y-0.5 flex items-center justify-center;
  }

  .view-all-button {
    @apply bg-orange-500 hover:bg-orange-600 text-white font-bold text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-1.5 rounded-md transition-colors;
  }

  .section-pagination {
    @apply flex space-x-1 items-center;
  }

  .pagination-dot {
    @apply w-2 h-2 rounded-full bg-white/30;
  }

  .pagination-dot-active {
    @apply w-2 h-2 rounded-full bg-white;
  }
}

@layer utilities {
  .btn-secondary {
    @apply bg-secondary text-white hover:bg-secondary/90 transition-colors;
  }

  .btn-primary {
    @apply bg-primary text-white hover:bg-primary/90 transition-colors;
  }

  .game-card {
    @apply bg-white rounded-md overflow-hidden shadow-md hover:shadow-lg transition-shadow;
  }

  .section-title {
    @apply text-white text-lg sm:text-xl md:text-2xl font-bold mb-4;
  }

  .nav-menu {
    @apply bg-green-600 rounded-lg px-1 py-1;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}

/* 添加背景图案样式 */
.bg-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%232563EB' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
}

/* 添加富文本内容样式 */
.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  color: #1e40af;
  margin-top: 1.5em;
  margin-bottom: 0.75em;
}

.prose p {
  margin-bottom: 1em;
}

.prose ul, .prose ol {
  padding-left: 1.5em;
  margin-bottom: 1em;
}

.prose li {
  margin-bottom: 0.5em;
}

.prose a {
  color: #2563eb;
  text-decoration: underline;
}

.prose strong {
  font-weight: 600;
}
