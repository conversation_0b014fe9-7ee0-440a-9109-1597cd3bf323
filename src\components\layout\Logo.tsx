import Link from "next/link";
import Image from "next/image";
import { useState } from "react";

export function Logo() {
  const [imageError, setImageError] = useState(false);

  return (
    <Link href="/" className="no-underline">
      <div className="flex items-center">
        <div className="bg-yellow-500 text-white h-10 w-10 rounded-md flex items-center justify-center font-bold text-2xl shadow-md">
          {!imageError ? (
            <Image 
              src="/kizi-logo.png" 
              alt="Drive Mad" 
              width={40} 
              height={40}
              className="object-contain"
              onError={() => setImageError(true)}
            />
          ) : (
            <span>K</span>
          )}
        </div>
        <div className="ml-1 flex flex-col">
          <span className="text-white font-bold text-xl">Drive Mad</span>
          <span className="text-white/70 text-xs">Games</span>
        </div>
      </div>
    </Link>
  );
}
