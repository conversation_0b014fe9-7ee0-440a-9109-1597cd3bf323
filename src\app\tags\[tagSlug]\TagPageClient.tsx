"use client";

import { useState, useEffect, useCallback } from "react";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { getGamesByTag, Game, normalizeImagePath } from "@/lib/apiService";
import Link from "next/link";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";

// Define API response interface
interface ApiResponse {
  status: string;
  data: Game[];
  total: number;
}

// Games per page
const GAMES_PER_PAGE = 20;

interface TagPageClientProps {
  tagSlug: string;
}

export default function TagPageClient({ tagSlug }: TagPageClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Get current page from URL, default to 1
  const currentPage = Number(searchParams.get('page') || '1');
  
  const [games, setGames] = useState<Game[]>([]);
  const [tagName, setTagName] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalGames, setTotalGames] = useState(0);
  const [totalPages, setTotalPages] = useState(1);

  // Helper function: Format slug into readable tag name
  function formatTagName(slug: string): string {
    return slug.charAt(0).toUpperCase() + slug.slice(1).replace(/-/g, ' ');
  }

  // Function to fetch games by tag
  const fetchGamesByTag = useCallback(async (page: number) => {
    try {
      setIsLoading(true);
      setError(null);
      
      console.log(`Fetching games for tag: ${tagSlug}, page: ${page}`);
      
      const offset = (page - 1) * GAMES_PER_PAGE;
      const result = await getGamesByTag(tagSlug, GAMES_PER_PAGE, offset);
      
      if (result.status === 'success' && result.data) {
        setGames(result.data);
        setTotalGames(result.total || result.data.length);
        setTotalPages(Math.ceil((result.total || result.data.length) / GAMES_PER_PAGE));
        
        // Set tag name from the first game's tag or format from slug
        if (result.data.length > 0 && result.data[0].tags) {
          const gameTag = result.data[0].tags.split(',').find(tag => 
            tag.trim().toLowerCase().replace(/\s+/g, '-') === tagSlug
          );
          setTagName(gameTag ? gameTag.trim() : formatTagName(tagSlug));
        } else {
          setTagName(formatTagName(tagSlug));
        }
      } else {
        setError(`No games found for tag "${formatTagName(tagSlug)}"`);
        setGames([]);
        setTotalGames(0);
        setTotalPages(1);
        setTagName(formatTagName(tagSlug));
      }
    } catch (err) {
      console.error("Error fetching games by tag:", err);
      setError("Failed to load games. Please try again later.");
      setGames([]);
      setTotalGames(0);
      setTotalPages(1);
      setTagName(formatTagName(tagSlug));
    } finally {
      setIsLoading(false);
    }
  }, [tagSlug]);

  // Load games when component mounts or page changes
  useEffect(() => {
    fetchGamesByTag(currentPage);
  }, [fetchGamesByTag, currentPage]);

  // Handle page navigation
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages && newPage !== currentPage) {
      const params = new URLSearchParams(searchParams.toString());
      params.set('page', newPage.toString());
      router.push(`/tags/${tagSlug}?${params.toString()}`);
    }
  };

  // Generate pagination numbers
  const getPaginationNumbers = () => {
    const delta = 2;
    const range = [];
    const rangeWithDots = [];

    for (let i = Math.max(2, currentPage - delta); 
         i <= Math.min(totalPages - 1, currentPage + delta); 
         i++) {
      range.push(i);
    }

    if (currentPage - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }

    rangeWithDots.push(...range);

    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push('...', totalPages);
    } else if (totalPages > 1) {
      rangeWithDots.push(totalPages);
    }

    return rangeWithDots;
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Page Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {tagName} Games
            </h1>
            <p className="text-gray-600">
              {isLoading ? "Loading..." : `${totalGames} games found`}
            </p>
          </div>

          {/* Loading State */}
          {isLoading && (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
              {Array.from({ length: GAMES_PER_PAGE }).map((_, index) => (
                <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
                  <div className="w-full h-48 bg-gray-200"></div>
                  <div className="p-4">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Error State */}
          {error && !isLoading && (
            <div className="text-center py-12">
              <div className="text-red-600 text-lg mb-4">{error}</div>
              <button 
                onClick={() => fetchGamesByTag(currentPage)}
                className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded-md transition-colors"
              >
                Try Again
              </button>
            </div>
          )}

          {/* Games Grid */}
          {!isLoading && !error && games.length > 0 && (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6 mb-8">
                {games.map((game) => (
                  <Link 
                    key={game.game_id} 
                    href={`/games/${game.game_url || game.game_name}`}
                    className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow group"
                  >
                    <div className="relative w-full h-48 bg-gray-100">
                      <Image
                        src={normalizeImagePath(game.image)}
                        alt={game.name}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-200"
                        sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, (max-width: 1280px) 25vw, 20vw"
                      />
                    </div>
                    <div className="p-4">
                      <h3 className="font-semibold text-gray-900 mb-1 line-clamp-2 group-hover:text-blue-600 transition-colors">
                        {game.name}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {game.category_name || 'Game'}
                      </p>
                    </div>
                  </Link>
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center items-center space-x-2">
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="px-3 py-2 rounded-md bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    Previous
                  </button>
                  
                  {getPaginationNumbers().map((pageNum, index) => (
                    <button
                      key={index}
                      onClick={() => typeof pageNum === 'number' ? handlePageChange(pageNum) : undefined}
                      disabled={pageNum === '...'}
                      className={`px-3 py-2 rounded-md transition-colors ${
                        pageNum === currentPage
                          ? 'bg-blue-600 text-white'
                          : pageNum === '...'
                          ? 'cursor-default text-gray-500'
                          : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                      }`}
                    >
                      {pageNum}
                    </button>
                  ))}
                  
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="px-3 py-2 rounded-md bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    Next
                  </button>
                </div>
              )}
            </>
          )}

          {/* Empty State */}
          {!isLoading && !error && games.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-600 text-lg mb-4">
                No games found for "{tagName}"
              </div>
              <Link 
                href="/games"
                className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded-md transition-colors"
              >
                Browse All Games
              </Link>
            </div>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
}
