/** @type {import('next').NextConfig} */
const nextConfig = {
  // 统一配置：始终不使用 trailingSlash
  trailingSlash: false,

  // 基础配置
  images: {
    unoptimized: true,
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'ext.same-assets.com',
      },
      {
        protocol: 'https',
        hostname: 'api.drivemad.store',
      },
    ],
    domains: ['api.drivemad.store'],
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },

  // 只在生产环境下启用静态导出
  ...(process.env.NODE_ENV === 'production' && {
    output: 'export',
    distDir: 'out',
  }),


};

export default nextConfig;
