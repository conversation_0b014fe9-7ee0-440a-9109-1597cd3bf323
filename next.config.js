/** @type {import('next').NextConfig} */
const isDev = process.env.NODE_ENV === 'development';
const isProd = process.env.NODE_ENV === 'production';

const nextConfig = {
  // 基础配置
  images: {
    unoptimized: true,
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'ext.same-assets.com',
      },
      {
        protocol: 'https',
        hostname: 'api.drivemad.store',
      },
    ],
    domains: ['api.drivemad.store'],
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },

  // 只在生产环境下启用静态导出相关配置
  ...(isProd && {
    output: 'export',
    distDir: 'out',
    trailingSlash: true,
  }),

  // 开发环境特定配置
  ...(isDev && {
    // 确保开发环境下不使用 trailingSlash
    trailingSlash: false,
  }),
};

export default nextConfig;
