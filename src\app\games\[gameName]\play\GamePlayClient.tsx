"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { getGameDetails, Game, addRecentGame } from "@/lib/apiService";
import { Header } from "@/components/layout/Header";

interface GamePlayClientProps {
  gameName: string;
  initialData: Game | null;
}

export default function GamePlayClient({ gameName, initialData }: GamePlayClientProps) {
  const [game, setGame] = useState<Game | null>(initialData);
  const [isLoading, setIsLoading] = useState(!initialData);
  const [error, setError] = useState<string | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    // 如果没有初始数据或者gameName变化了，才从客户端获取数据
    if (!initialData || !game) {
      async function fetchGameData() {
        try {
          setIsLoading(true);
          const result = await getGameDetails(gameName);

          if (result.status === 'success' && result.data) {
            setGame(result.data);
            setError(null);
            
            // 将游戏添加到最近游戏列表
            addRecentGame(result.data);
          } else {
            setError("Game not found");
          }
        } catch (err) {
          console.error("Error fetching game details:", err);
          setError("Failed to load game");
        } finally {
          setIsLoading(false);
        }
      }

      fetchGameData();
    } else if (initialData) {
      // 如果有初始数据，将游戏添加到最近游戏列表
      addRecentGame(initialData);
    }
  }, [gameName, initialData, game]);

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      // 进入全屏模式
      const iframe = iframeRef.current;
      if (iframe && iframe.requestFullscreen) {
        iframe.requestFullscreen().catch(err => {
          console.error(`Error attempting to enable fullscreen: ${err.message}`);
        });
      }
    } else {
      // 退出全屏模式
      if (document.exitFullscreen) {
        document.exitFullscreen().catch(err => {
          console.error(`Error attempting to exit fullscreen: ${err.message}`);
        });
      }
    }
  };

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    
    // 确保iframe占据足够的高度
    const updateIframeHeight = () => {
      const navbarHeight = 56; // 顶部导航栏的高度
      const windowHeight = window.innerHeight;
      const iframe = iframeRef.current;
      
      if (iframe) {
        iframe.style.height = `${windowHeight - navbarHeight}px`;
      }
    };
    
    // 初始调整和窗口大小变化时调整
    updateIframeHeight();
    window.addEventListener('resize', updateIframeHeight);
    
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      window.removeEventListener('resize', updateIframeHeight);
    };
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <div className="flex-grow flex items-center justify-center">
          <div className="w-16 h-16 border-t-4 border-b-4 border-blue-500 rounded-full animate-spin"></div>
        </div>
      </div>
    );
  }

  if (error || !game) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <div className="flex-grow flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">Game Not Found</h1>
            <p className="text-gray-600 mb-6">Sorry, the game you are looking for is not available.</p>
            <Link href="/" className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded-md transition-colors">
              Back to Home
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-gray-900">
      {/* 顶部导航栏 */}
      <div className="bg-gray-800 text-white py-3 px-4 flex items-center justify-between">
        <div className="flex items-center">
          <Link 
            href={`/games/${game.game_name}`} 
            className="flex items-center hover:text-blue-300 transition-colors"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-5 w-5 mr-2" 
              viewBox="0 0 20 20" 
              fill="currentColor"
            >
              <path 
                fillRule="evenodd" 
                d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" 
                clipRule="evenodd" 
              />
            </svg>
            Back
          </Link>
        </div>
        
        <h1 className="text-lg font-medium truncate mx-4 text-center flex-grow">
          {game.name}
        </h1>
        
        <button 
          onClick={toggleFullscreen}
          className="p-2 rounded-md hover:bg-gray-700 transition-colors"
          aria-label={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-5 w-5" 
            fill="none"
            viewBox="0 0 24 24" 
            stroke="currentColor"
            strokeWidth="2"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" 
            />
          </svg>
        </button>
      </div>
      
      {/* 游戏iframe容器 */}
      <div className="flex-grow w-full bg-black">
        <iframe
          ref={iframeRef}
          src={game.file}
          className="w-full h-full border-0"
          allowFullScreen
          title={game.name}
          sandbox="allow-same-origin allow-scripts allow-popups allow-forms allow-presentation allow-top-navigation"
          allow="fullscreen; autoplay; clipboard-write; cross-origin-isolated"
          loading="eager"
          referrerPolicy="no-referrer"
          style={{ display: 'block' }}
        ></iframe>
      </div>
    </div>
  );
}
