import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import ClientBody from "./ClientBody";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Drive Mad - Fun Physics-Based Driving Game | Play 100+ Challenging Levels",
  description: "Play Drive Mad, the addictive physics-based driving game featuring 100+ challenging levels. Navigate cars through crazy obstacles, collect upgrades, and explore player-created content. Available on mobile and desktop browsers.",
  keywords: "Drive Mad game, physics driving, car puzzle game, driving challenges, obstacle course game, browser games, free online games, Martin <PERSON>, addictive driving game, car physics",
  openGraph: {
    title: "Drive Mad - Physics-Based Driving Game with Endless Challenges",
    description: "Drive through dangerous terrain, conquer obstacles, and complete challenges in this addictive physics-based driving game. Free to play online!",
    images: ['/images/og-image.jpg'],
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "Drive Mad - Crazy Physics Driving Game",
    description: "Navigate cars through wild terrain and overcome challenging obstacles in this fun physics-based driving game!",
    images: ['/images/twitter-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${inter.variable}`}>
      <ClientBody>{children}</ClientBody>
    </html>
  );
}
