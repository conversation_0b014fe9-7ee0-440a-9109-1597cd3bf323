"use client";

import { useState, useEffect, Suspense } from "react";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { getTags, Tag } from "@/lib/apiService";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";

// Number of tags displayed per page
const TAGS_PER_PAGE = 30;

// 内部客户端组件，包含使用 useSearchParams 的逻辑
function TagsContent() {
  const [tags, setTags] = useState<Tag[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filteredTags, setFilteredTags] = useState<Tag[]>([]);
  const [currentPageTags, setCurrentPageTags] = useState<Tag[]>([]);
  const [totalPages, setTotalPages] = useState(1);
  const [error, setError] = useState<string | null>(null);
  
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Get current page from URL, default is 1
  const currentPage = Number(searchParams.get('page') || '1');

  // Function to fetch tags data
  async function fetchTags() {
    try {
      setIsLoading(true);
      setError(null);
      
      console.log('Fetching tags data...');
      const result = await getTags();
      
      if (result.status === 'success' && result.data && Array.isArray(result.data)) {
        // Ensure each tag has a url property
        const processedTags = result.data.map((tag: Tag) => ({
          ...tag,
          // If no url exists, create one from the name
          url: tag.url || tag.name.toLowerCase().replace(/\s+/g, '-')
        }));
        
        console.log(`Fetched ${processedTags.length} tags from API`);
        
        // Check if using default data
        if (processedTags.length <= 8 && 
            processedTags.some(tag => tag.name === 'New') && 
            processedTags.some(tag => tag.name === 'Popular')) {
          setError("Showing default tag data. API data is currently unavailable.");
        } else {
          setError(null);
        }
        
        setTags(processedTags);
        setFilteredTags(processedTags);
      } else {
        console.error("Invalid tags data format:", result);
        setError("Unable to retrieve valid tag data.");
        setTags([]);
        setFilteredTags([]);
      }
    } catch (error) {
      console.error("Error fetching tags:", error);
      setError("Error fetching tag data. Please try again later.");
      setTags([]);
      setFilteredTags([]);
    } finally {
      setIsLoading(false);
    }
  }

  // Load tags data on initial render
  useEffect(() => {
    fetchTags();
  }, []);
  
  // Calculate total pages and current page tags
  useEffect(() => {
    if (filteredTags.length > 0) {
      // Calculate total pages
      const pages = Math.ceil(filteredTags.length / TAGS_PER_PAGE);
      setTotalPages(pages);
      
      // Get tags for the current page
      const startIndex = (currentPage - 1) * TAGS_PER_PAGE;
      const endIndex = startIndex + TAGS_PER_PAGE;
      setCurrentPageTags(filteredTags.slice(startIndex, endIndex));
    } else {
      setTotalPages(1);
      setCurrentPageTags([]);
    }
  }, [filteredTags, currentPage]);
  
  // Handle page navigation
  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages) return;
    
    // Update URL with page number
    if (page === 1) {
      router.push(`/tags`); // First page doesn't show page parameter
    } else {
      router.push(`/tags?page=${page}`);
    }
  };
  
  // Generate pagination buttons
  const renderPagination = () => {
    // If only one page, don't show pagination
    if (totalPages <= 1) return null;

    // Determine which page numbers to show
    const pageNumbers = [];
    // Always show first page
    pageNumbers.push(1);
    
    // Show pages around current page
    let start = Math.max(2, currentPage - 2);
    let end = Math.min(totalPages - 1, currentPage + 2);
    
    // If start page is not the second page, add ellipsis
    if (start > 2) {
      pageNumbers.push('...'); 
    }
    
    // Add middle pages
    for (let i = start; i <= end; i++) {
      pageNumbers.push(i);
    }
    
    // If end page is not the second-to-last page, add ellipsis
    if (end < totalPages - 1) {
      pageNumbers.push('...');
    }
    
    // If total pages is greater than 1, always show last page
    if (totalPages > 1) {
      pageNumbers.push(totalPages);
    }

    return (
      <div className="flex flex-col items-center mt-12 mb-8">
        <div className="flex justify-center items-center space-x-2 bg-white p-3 rounded-lg shadow-md">
          {/* Previous page button */}
          <button 
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className={`px-4 py-2 rounded-md transition-all duration-200 font-medium ${
              currentPage === 1 
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                : 'bg-blue-100 text-blue-700 hover:bg-blue-200 hover:scale-105'
            }`}
            aria-label="Previous Page"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          
          {/* Page number buttons */}
          {pageNumbers.map((page, index) => (
            page === '...' ? (
              <span key={`ellipsis-${index}`} className="px-3 py-2 text-gray-500">...</span>
            ) : (
              <button
                key={`page-${page}`}
                onClick={() => handlePageChange(Number(page))}
                className={`px-4 py-2 rounded-md transition-all duration-200 ${
                  Number(page) === currentPage 
                    ? 'bg-blue-600 text-white font-bold scale-110' 
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-700 hover:scale-105'
                }`}
              >
                {page}
              </button>
            )
          ))}
          
          {/* Next page button */}
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className={`px-4 py-2 rounded-md transition-all duration-200 font-medium ${
              currentPage === totalPages 
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                : 'bg-blue-100 text-blue-700 hover:bg-blue-200 hover:scale-105'
            }`}
            aria-label="Next Page"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="relative mb-10 bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg overflow-hidden">
        <div className="absolute inset-0 opacity-10 bg-pattern"></div>
        <div className="relative z-10 px-6 py-10 md:px-10 text-white">
          <div>
            <h1 className="text-3xl md:text-4xl font-bold mb-4">Game Tags</h1>
            <p className="text-blue-100 max-w-3xl">
              Browse our collection of games by tags. Find games that match your interests and preferences.
            </p>
          </div>
        </div>
      </div>
      
      {/* Error Message */}
      {error && (
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">{error}</p>
            </div>
          </div>
        </div>
      )}
      
      {/* Tags Grid */}
      {isLoading ? (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
          {Array.from({ length: TAGS_PER_PAGE }).map((_, index) => (
            <div key={index} className="h-12 bg-blue-100 rounded-lg animate-pulse"></div>
          ))}
        </div>
      ) : filteredTags.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-500 text-lg">No tags found</div>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
            {currentPageTags.map((tag) => (
              <Link 
                key={tag.id} 
                href={`/tags/${tag.url}`}
                className="bg-white hover:bg-blue-50 border border-gray-200 rounded-lg py-3 px-4 flex items-center transition-colors shadow-sm hover:shadow-md"
              >
                <div className="flex items-center justify-between w-full">
                  <span className="font-medium text-gray-800">{tag.name}</span>
                  <svg 
                    className="w-5 h-5 text-blue-600" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </Link>
            ))}
          </div>
          
          {/* Pagination controls */}
          {renderPagination()}
        </>
      )}
    </div>
  );
}

// 主页面组件
export default function TagsPage() {
  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <Header />
      <main className="flex-grow container mx-auto px-4 py-8">
        <Suspense fallback={
          <div className="max-w-6xl mx-auto">
            <div className="relative mb-10 bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg overflow-hidden">
              <div className="absolute inset-0 opacity-10 bg-pattern"></div>
              <div className="relative z-10 px-6 py-10 md:px-10 text-white animate-pulse">
                <div className="h-8 bg-blue-400/50 rounded w-1/2 mb-4"></div>
                <div className="h-4 bg-blue-400/50 rounded w-3/4"></div>
              </div>
            </div>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
              {Array.from({ length: TAGS_PER_PAGE }).map((_, index) => (
                <div key={index} className="h-12 bg-blue-100 rounded-lg animate-pulse"></div>
              ))}
            </div>
          </div>
        }>
          <TagsContent />
        </Suspense>
      </main>
      <Footer />
    </div>
  );
} 