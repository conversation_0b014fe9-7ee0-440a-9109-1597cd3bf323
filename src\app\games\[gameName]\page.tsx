import { Suspense } from "react";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { getGameDetails, getPopularGames, getLatestGames } from "@/lib/apiService";
import GameDetailsClient from "./GameDetailsClient";

interface GameParams {
  params: Promise<{
    gameName: string;
  }>;
}

// 生成静态参数用于预渲染游戏页面
export async function generateStaticParams() {
  try {
    // 获取热门游戏和最新游戏的列表
    const [popularResult, latestResult] = await Promise.all([
      getPopularGames(50), // 获取前50个热门游戏
      getLatestGames(50)   // 获取前50个最新游戏
    ]);

    const gameNames = new Set<string>();

    // 添加热门游戏
    if (popularResult.status === 'success' && popularResult.data) {
      popularResult.data.forEach(game => {
        if (game.game_url) {
          gameNames.add(game.game_url);
        }
      });
    }

    // 添加最新游戏
    if (latestResult.status === 'success' && latestResult.data) {
      latestResult.data.forEach(game => {
        if (game.game_url) {
          gameNames.add(game.game_url);
        }
      });
    }

    // 确保包含主要游戏
    gameNames.add('drive-mad');

    // 转换为参数格式
    return Array.from(gameNames).map((gameName) => ({
      gameName: gameName,
    }));
  } catch (error) {
    console.error('Error generating static params for games:', error);
    // 返回基本的游戏列表作为备用
    return [
      { gameName: 'drive-mad' },
    ];
  }
}

export default async function GameDetailsPage({ params }: GameParams) {
  // 确保先解析params参数
  const resolvedParams = await params;
  const gameName = resolvedParams.gameName;

  // 在服务端获取游戏数据
  let initialData = null;

  try {
    const gameResult = await getGameDetails(gameName);
    if (gameResult.status === 'success') {
      initialData = gameResult.data;
    }
  } catch (error) {
    console.error("Error fetching game details on server:", error);
  }

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <Suspense fallback={
            <div className="bg-white rounded-lg shadow-md p-6 animate-pulse">
              <div className="w-full h-[400px] bg-blue-200 rounded-md mb-6"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-6"></div>
              <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-6"></div>
              <div className="flex space-x-2 mb-6">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="h-8 bg-blue-100 rounded-full px-4 w-20"></div>
                ))}
              </div>
              <div className="h-12 bg-green-200 rounded-lg w-40"></div>
            </div>
          }>
            <GameDetailsClient
              gameName={gameName}
              initialData={initialData}
            />
          </Suspense>
        </div>
      </main>
      <Footer />
    </div>
  );
}