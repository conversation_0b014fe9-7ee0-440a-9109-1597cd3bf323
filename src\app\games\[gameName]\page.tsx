import { Suspense } from "react";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { getGameDetails } from "@/lib/apiService";
import GameDetailsClient from "./GameDetailsClient";

interface GameParams {
  params: Promise<{
    gameName: string;
  }>;
}

export default async function GameDetailsPage({ params }: GameParams) {
  // 确保先解析params参数
  const resolvedParams = await params;
  const gameName = resolvedParams.gameName;
  
  // 在服务端获取游戏数据
  let initialData = null;
  
  try {
    const gameResult = await getGameDetails(gameName);
    if (gameResult.status === 'success') {
      initialData = gameResult.data;
    }
  } catch (error) {
    console.error("Error fetching game details on server:", error);
  }

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <Suspense fallback={
            <div className="bg-white rounded-lg shadow-md p-6 animate-pulse">
              <div className="w-full h-[400px] bg-blue-200 rounded-md mb-6"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-6"></div>
              <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-6"></div>
              <div className="flex space-x-2 mb-6">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="h-8 bg-blue-100 rounded-full px-4 w-20"></div>
                ))}
              </div>
              <div className="h-12 bg-green-200 rounded-lg w-40"></div>
            </div>
          }>
            <GameDetailsClient 
              gameName={gameName} 
              initialData={initialData}
            />
          </Suspense>
        </div>
      </main>
      <Footer />
    </div>
  );
} 