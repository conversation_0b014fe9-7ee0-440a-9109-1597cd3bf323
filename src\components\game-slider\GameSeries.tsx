"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";

interface GameSeriesItem {
  id: string;
  title: string;
  imageUrl: string;
}

interface GameSeriesProps {
  title: string;
  series: GameSeriesItem[];
  showPagination?: boolean;
}

export function GameSeries({ title, series, showPagination = true }: GameSeriesProps) {
  const [currentPage, setCurrentPage] = useState(0);
  
  const itemsPerPage = 6; // 一页显示6个游戏系列，从7改为6
  const totalPages = Math.ceil(series.length / itemsPerPage);
  
  // 计算当前页要显示的游戏系列
  const startIndex = currentPage * itemsPerPage;
  const visibleSeries = series.slice(startIndex, startIndex + itemsPerPage);

  const handlePageChange = (newPage: number) => {
    if (newPage >= 0 && newPage < totalPages) {
      setCurrentPage(newPage);
    }
  };

  return (
    <div className="mb-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="section-title">
          {title}
        </h2>
        {showPagination && (
          <div className="section-pagination">
            {Array.from({ length: totalPages }).map((_, index) => (
              <div 
                key={index} 
                className={`${index === currentPage ? "pagination-dot-active" : "pagination-dot"} mx-0.5 cursor-pointer`}
                onClick={() => handlePageChange(index)}
              />
            ))}
            <div className="ml-2 flex">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                className={`bg-yellow-500 text-white h-6 w-6 flex items-center justify-center rounded-l-md ${currentPage === 0 ? 'opacity-50 cursor-not-allowed' : ''}`}
                disabled={currentPage === 0}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
              </button>
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                className={`bg-yellow-500 text-white h-6 w-6 flex items-center justify-center rounded-r-md ${currentPage === totalPages - 1 ? 'opacity-50 cursor-not-allowed' : ''}`}
                disabled={currentPage === totalPages - 1}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </button>
            </div>
          </div>
        )}
      </div>
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2">
        {visibleSeries.map((item) => (
          <Link
            key={item.id}
            href={`/games/${item.id}`}
            className="block"
          >
            <div className="bg-blue-600 rounded-md p-2 flex items-center h-[80px] group hover:bg-blue-700 transition-colors">
              <div className="relative w-[50px] h-[50px] flex-shrink-0">
                <Image
                  src={item.imageUrl}
                  alt={item.title}
                  width={50}
                  height={50}
                  className="object-contain"
                />
              </div>
              <div className="ml-2 text-white text-sm truncate">{item.title}</div>
              <div className="ml-auto">
                <div className="bg-green-500 text-white h-6 w-6 flex items-center justify-center rounded-md">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
}
