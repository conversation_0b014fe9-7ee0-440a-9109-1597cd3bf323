import { NextRequest, NextResponse } from 'next/server';

// API基础URL
const API_BASE_URL = "https://api.drivemad.store";

export async function GET(request: NextRequest) {
  try {
    // 获取请求的page_url参数
    const { searchParams } = new URL(request.url);
    const pageUrl = searchParams.get('page_url') || '/';

    console.log(`[API Route] Proxying request for page: ${pageUrl}`);

    // 构建API请求URL - 避免使用URL对象再次解析
    const apiUrl = `${API_BASE_URL}/pages/?page_url=${encodeURIComponent(pageUrl)}&appname=drivemadstore`;
    console.log(`[API Route] Forwarding to: ${apiUrl}`);

    // 使用node-fetch风格的请求
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      },
      cache: 'no-store' // 不缓存，始终获取最新数据
    });

    // 检查API响应状态
    if (!response.ok) {
      console.error(`[API Route] API responded with error: ${response.status} ${response.statusText}`);
      
      // 尝试直接读取外部API数据作为备选
      try {
        // 如果API请求失败，返回默认数据
        let defaultData = {
          status: "success",
          data: {
            page_name: pageUrl.replace(/^\//, ''),
            page_url: pageUrl,
            description: pageUrl === '/privacy' ? 'Privacy Policy' : 'Drive Mad',
            has_content: "1",
            content_value: pageUrl === '/privacy' 
              ? "<p>This Privacy Policy describes how your personal information is collected, used, and shared when you visit our website.</p><p>We are committed to protecting your privacy and ensuring that your personal information is handled securely.</p>"
              : "Welcome to Drive Mad!"
          }
        };
        
        return NextResponse.json(defaultData);
      } catch (err) {
        return NextResponse.json(
          { 
            status: 'error', 
            message: `API responded with status ${response.status}` 
          }, 
          { status: 200 } // 使用200状态码，但在内容中标记错误
        );
      }
    }

    // 读取API响应
    const data = await response.json();
    console.log(`[API Route] Successfully proxied page data for ${pageUrl}`);

    // 返回数据到客户端
    return NextResponse.json(data);
  } catch (error) {
    console.error('[API Route] Error proxying page request:', error);
    
    // 返回默认数据而不是错误
    const pageUrl = request.url.includes('privacy') ? '/privacy' : '/';
    const defaultData = {
      status: "success",
      data: {
        page_name: pageUrl.replace(/^\//, '') || 'home',
        page_url: pageUrl,
        description: pageUrl === '/privacy' ? 'Privacy Policy' : 'Drive Mad',
        has_content: "1",
        content_value: pageUrl === '/privacy' 
          ? "<p>This Privacy Policy describes how your personal information is collected, used, and shared when you visit our website.</p><p>We are committed to protecting your privacy and ensuring that your personal information is handled securely.</p>"
          : "Welcome to Drive Mad!"
      }
    };
    
    return NextResponse.json(defaultData);
  }
} 