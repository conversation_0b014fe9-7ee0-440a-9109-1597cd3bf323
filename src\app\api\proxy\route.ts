import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic'; // 禁用路由缓存
export const revalidate = 0; // 禁用路由级别缓存

// 打印调试信息到服务器控制台
function logDebug(message: string, ...args: any[]) {
  console.log(`[PROXY] ${message}`, ...args);
}

// 记录错误到服务器控制台
function logError(message: string, error: any) {
  console.error(`[PROXY ERROR] ${message}`, error);
}

// 通用代理处理函数，同时处理GET和POST请求
async function proxyRequest(request: NextRequest, method: string) {
  try {
    logDebug(`Received ${method} proxy request`);
    
    // 获取URL参数
    const urlParam = request.nextUrl.searchParams.get('url');
    const error = request.nextUrl.searchParams.get('error');
    
    // 如果是错误URL请求，直接返回错误信息
    if (error === 'invalid_url') {
      const originalUrl = request.nextUrl.searchParams.get('original') || 'unknown_url';
      logDebug('Invalid URL request detected', { originalUrl });
      return NextResponse.json(
        { error: 'Invalid URL provided', original_url: originalUrl },
        { status: 400 }
      );
    }

    if (!urlParam) {
      logDebug('Missing URL parameter');
      return NextResponse.json(
        { error: 'Missing url parameter' },
        { status: 400 }
      );
    }

    logDebug('Processing URL', { urlParam });
    
    // 手动解析URL，确保正确处理
    let targetUrl = urlParam;
    let hostname = '';
    
    try {
      // 尝试使用URL对象解析
      const urlObj = new URL(urlParam);
      hostname = urlObj.hostname;
      
      // 使用原始字符串作为目标URL
      targetUrl = urlParam;
      logDebug('URL parsed successfully with URL object', { hostname });
    } catch (e) {
      logError('URL parsing error', e);
      
      // 尝试手动修复URL
      if (urlParam.startsWith('http:') || urlParam.startsWith('https:')) {
        // 有协议前缀但仍然无效
        return NextResponse.json(
          { error: 'Invalid URL format', url: urlParam },
          { status: 400 }
        );
      } else {
        // 尝试添加协议前缀
        targetUrl = `https://${urlParam}`;
        try {
          const urlObj = new URL(targetUrl);
          hostname = urlObj.hostname;
          logDebug('Fixed URL by adding protocol', { targetUrl });
        } catch (e2) {
          logError('Still invalid after adding protocol', e2);
          
          // 尝试额外的修复方法
          if (urlParam.includes('tapixyapi.qilaigames.com') || urlParam.includes('api.drivemad.store')) {
            // 尝试提取域名并重建URL
            const domainMatch = urlParam.match(/(tapixyapi\.qilaigames\.com|api\.drivemad\.store)/);
            if (domainMatch) {
              const domain = domainMatch[0];
              const pathMatch = urlParam.match(/(?:tapixyapi\.qilaigames\.com|api\.drivemad\.store)(.*)/);
              const path = pathMatch ? pathMatch[1] : '/';
              
              targetUrl = `https://${domain}${path}`;
              try {
                const urlObj = new URL(targetUrl);
                hostname = urlObj.hostname;
                logDebug('Fixed URL by reconstructing from domain parts', { targetUrl });
              } catch (e3) {
                logError('Failed all URL fixing attempts', e3);
                return NextResponse.json(
                  { error: 'Invalid URL format after multiple fixing attempts', url: urlParam },
                  { status: 400 }
                );
              }
            } else {
              return NextResponse.json(
                { error: 'Invalid URL format even after adding protocol', url: urlParam },
                { status: 400 }
              );
            }
          } else {
            return NextResponse.json(
              { error: 'Invalid URL format even after adding protocol', url: urlParam },
              { status: 400 }
            );
          }
        }
      }
    }
    
    logDebug('URL processed successfully', { targetUrl, hostname });

    // 检查URL是否来自允许的域名
    const allowedDomains = [
      'api.drivemad.store'
    ];
    
    const isAllowed = allowedDomains.some(domain => 
      hostname === domain || hostname.endsWith(`.${domain}`)
    );

    if (!isAllowed) {
      logDebug('URL domain not allowed', { hostname });
      return NextResponse.json(
        { error: 'URL domain not allowed', domain: hostname },
        { status: 403 }
      );
    }

    // 记录分类相关请求
    if (targetUrl.includes('/categories')) {
      logDebug('Detected categories API request', { targetUrl });
    }

    logDebug(`Making ${method} fetch request to target URL`, { targetUrl });
    
    // 准备fetch选项
    const fetchOptions: RequestInit = {
      method,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json',
      },
      cache: 'no-store',
    };
    
    // 如果是POST请求，尝试读取请求主体
    if (method === 'POST') {
      try {
        const contentType = request.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const body = await request.json();
          fetchOptions.body = JSON.stringify(body);
          fetchOptions.headers = {
            ...fetchOptions.headers,
            'Content-Type': 'application/json',
          };
          logDebug('Added JSON body to POST request', { body });
        } else if (contentType && contentType.includes('application/x-www-form-urlencoded')) {
          const formData = await request.formData();
          const formBody = new URLSearchParams();
          
          // 转换FormData为URLSearchParams
          for (const [key, value] of formData.entries()) {
            formBody.append(key, value.toString());
          }
          
          fetchOptions.body = formBody.toString();
          fetchOptions.headers = {
            ...fetchOptions.headers,
            'Content-Type': 'application/x-www-form-urlencoded',
          };
          logDebug('Added form data to POST request');
        } else {
          // 其他情况，尝试作为文本读取
          const text = await request.text();
          if (text) {
            fetchOptions.body = text;
            logDebug('Added text body to POST request', { length: text.length });
          }
        }
      } catch (bodyError) {
        logError('Error reading request body', bodyError);
        // 继续处理请求，即使没有主体
      }
    }
    
    // 执行代理请求
    try {
      const response = await fetch(targetUrl, fetchOptions);

      logDebug('Received response', {
        status: response.status, 
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers)
      });

      if (!response.ok) {
        logDebug('Non-OK response from target API', { status: response.status, statusText: response.statusText });
      }

      // 读取响应内容
      try {
        const data = await response.json();
        
        // 返回响应
        logDebug('Returning JSON response to client');
        return NextResponse.json(data, {
          status: response.status,
          headers: {
            'Cache-Control': 'no-store, max-age=0',
            'Access-Control-Allow-Origin': '*',
          },
        });
      } catch (jsonError) {
        logError('Error parsing JSON response', jsonError);
        
        try {
          // 如果JSON解析失败，尝试获取文本
          const text = await response.text();
          return new NextResponse(text, {
            status: response.status,
            headers: {
              'Content-Type': 'text/plain',
              'Cache-Control': 'no-store, max-age=0',
              'Access-Control-Allow-Origin': '*',
            },
          });
        } catch (textError) {
          logError('Error getting response text', textError);
          throw new Error('Failed to parse response from target API');
        }
      }
    } catch (fetchError) {
      logError('Fetch error', fetchError);
      return NextResponse.json(
        { 
          error: 'Error fetching from target API', 
          message: fetchError instanceof Error ? fetchError.message : String(fetchError),
          url: targetUrl
        },
        { status: 502 }
      );
    }
  } catch (error) {
    logError('Unhandled proxy error', error);
    return NextResponse.json(
      { error: 'Proxy request failed', message: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

// GET请求处理
export async function GET(request: NextRequest) {
  return proxyRequest(request, 'GET');
}

// POST请求处理
export async function POST(request: NextRequest) {
  return proxyRequest(request, 'POST');
}

// OPTIONS请求处理，用于CORS预检请求
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
} 