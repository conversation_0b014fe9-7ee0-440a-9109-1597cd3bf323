export default function SearchLoading() {
  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-8 bg-blue-600 text-white p-6 rounded-lg shadow-md animate-pulse">
        <div className="h-10 w-3/4 bg-blue-500 rounded mb-3"></div>
        <div className="h-6 w-40 bg-blue-500 rounded"></div>
      </div>

      <div className="w-full">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5">
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="bg-blue-600 rounded-lg overflow-hidden shadow-md">
              <div className="w-full aspect-video bg-blue-500 animate-pulse relative">
                <div className="absolute bottom-2 right-2 h-4 w-14 bg-green-600 rounded-full"></div>
              </div>
              <div className="p-4">
                <div className="h-6 bg-blue-500 rounded animate-pulse mb-2"></div>
                <div className="flex justify-between mt-2">
                  <div className="h-5 w-20 bg-blue-700 rounded-full animate-pulse"></div>
                  <div className="h-5 w-12 bg-green-600 rounded-full animate-pulse"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
} 