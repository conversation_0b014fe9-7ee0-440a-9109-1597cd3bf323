/*
 Navicat Premium Dump SQL

 Source Server         : 欢乐园-美国1(candyjump)-*************
 Source Server Type    : MySQL
 Source Server Version : 50744 (5.7.44-log)
 Source Host           : *************:3306
 Source Schema         : candyjumpapp

 Target Server Type    : MySQL
 Target Server Version : 50744 (5.7.44-log)
 File Encoding         : 65001

 Date: 01/04/2025 17:44:14
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for gm_account
-- ----------------------------
DROP TABLE IF EXISTS `gm_account`;
CREATE TABLE `gm_account`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` text CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `username` varchar(250) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `password` varchar(250) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `admin` enum('0','1') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '0',
  `email` varchar(250) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `avatar_id` int(11) NOT NULL,
  `xp` int(11) NOT NULL,
  `language` varchar(250) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `profile_theme` varchar(250) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT 'style-1',
  `ip` varchar(250) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `registration_date` int(11) NOT NULL,
  `last_logged` int(11) NOT NULL,
  `last_update_info` int(11) NOT NULL,
  `active` enum('1','0') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for gm_ads
-- ----------------------------
DROP TABLE IF EXISTS `gm_ads`;
CREATE TABLE `gm_ads`  (
  `id` int(11) NOT NULL,
  `728x90` varchar(700) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `300x250` varchar(700) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `600x300` varchar(700) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `728x90_main` varchar(700) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `300x250_main` varchar(700) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `ads_video` varchar(700) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for gm_blogs
-- ----------------------------
DROP TABLE IF EXISTS `gm_blogs`;
CREATE TABLE `gm_blogs`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `image_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `post` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `date_created` date NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for gm_categories
-- ----------------------------
DROP TABLE IF EXISTS `gm_categories`;
CREATE TABLE `gm_categories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_pilot` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `name` text CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `image` varchar(400) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `footer_description` text CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL,
  `show_home` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8 COLLATE = utf8_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for gm_chatgpt
-- ----------------------------
DROP TABLE IF EXISTS `gm_chatgpt`;
CREATE TABLE `gm_chatgpt`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `api_key` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `template_game` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `template_category` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `template_tags` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `template_footer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `random_words_before_tags` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `random_words_after_tags` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `chatgpt_model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'gpt-3.5-turbo',
  `maximum_words` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for gm_footer_description
-- ----------------------------
DROP TABLE IF EXISTS `gm_footer_description`;
CREATE TABLE `gm_footer_description`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `page_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `page_url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `has_content` enum('1','0') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0',
  `content_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for gm_games
-- ----------------------------
DROP TABLE IF EXISTS `gm_games`;
CREATE TABLE `gm_games` (
  `game_id` int(11) NOT NULL AUTO_INCREMENT,
  `catalog_id` varchar(250) NOT NULL,
  `game_name` varchar(250) NOT NULL,
  `name` varchar(250) NOT NULL,
  `image` varchar(500) NOT NULL,
  `import` enum('0','1') NOT NULL DEFAULT '0',
  `category` int(11) NOT NULL,
  `plays` int(11) NOT NULL,
  `rating` enum('0','0.5','1','1.5','2','2.5','3','3.5','4','4.5','5') NOT NULL DEFAULT '0',
  `description` varchar(15000) NOT NULL,
  `instructions` varchar(600) NOT NULL,
  `file` varchar(500) NOT NULL,
  `game_type` varchar(250) NOT NULL,
  `w` int(10) NOT NULL,
  `h` int(10) NOT NULL,
  `date_added` int(11) NOT NULL,
  `published` enum('0','1') NOT NULL,
  `featured` enum('0','1') NOT NULL DEFAULT '0',
  `mobile` int(255) NOT NULL,
  `featured_sorting` varchar(255) NOT NULL,
  `field_1` varchar(500) NOT NULL,
  `field_2` varchar(500) NOT NULL,
  `field_3` varchar(500) NOT NULL,
  `field_4` varchar(500) NOT NULL,
  `field_5` varchar(500) NOT NULL,
  `field_6` varchar(500) NOT NULL,
  `field_7` varchar(500) NOT NULL,
  `field_8` varchar(500) NOT NULL,
  `field_9` varchar(500) NOT NULL,
  `field_10` varchar(500) NOT NULL,
  `tags_ids` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin,
  `video_url` varchar(100) DEFAULT NULL,
  `is_last_rewrite` tinyint(1) NOT NULL DEFAULT '0',
  `like_count` int(11) DEFAULT '0',
  `favorite_count` int(11) DEFAULT '0',
  PRIMARY KEY (`game_id`)
) ENGINE=InnoDB AUTO_INCREMENT=25038 DEFAULT CHARSET=latin1;

-- ----------------------------
-- Table structure for gm_links
-- ----------------------------
DROP TABLE IF EXISTS `gm_links`;
CREATE TABLE `gm_links`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 0,
  `is_chatgpt` tinyint(1) NOT NULL,
  `language_list` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'de,es,fr,it',
  `rewrite_method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `google_translate_language` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `last_id` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for gm_media
-- ----------------------------
DROP TABLE IF EXISTS `gm_media`;
CREATE TABLE `gm_media`  (
  `id` int(250) NOT NULL AUTO_INCREMENT,
  `name` varchar(250) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `extension` varchar(250) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT 'none',
  `type` varchar(250) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT 'none',
  `url` varchar(250) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for gm_setting
-- ----------------------------
DROP TABLE IF EXISTS `gm_setting`;
CREATE TABLE `gm_setting`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `site_name` varchar(500) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `site_url` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `site_theme` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `site_description` varchar(500) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT 'Best Free Online Games',
  `site_keywords` varchar(500) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT 'games, online, arcade, html5, gamemonetize',
  `ads_status` enum('0','1') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0',
  `ad_time` int(11) NOT NULL DEFAULT 10,
  `language` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `featured_game_limit` int(11) NULL DEFAULT NULL,
  `mp_game_limit` int(11) NULL DEFAULT NULL,
  `xp_play` int(11) NULL DEFAULT NULL,
  `xp_report` int(11) NULL DEFAULT NULL,
  `xp_register` int(11) NULL DEFAULT NULL,
  `plays` int(255) NOT NULL,
  `custom_game_feed_url` varchar(1000) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL,
  `settings_1` varchar(500) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `settings_2` varchar(500) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `settings_3` varchar(500) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `settings_4` varchar(500) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `settings_5` varchar(500) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `settings_6` varchar(500) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `settings_7` varchar(500) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `settings_8` varchar(500) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `settings_9` varchar(500) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `settings_10` varchar(500) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `recaptcha_site_key` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL,
  `recaptcha_secret_key` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL,
  `is_sidebar_enabled` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for gm_sidebar
-- ----------------------------
DROP TABLE IF EXISTS `gm_sidebar`;
CREATE TABLE `gm_sidebar`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `category_tags_id` int(11) NULL DEFAULT NULL,
  `custom_link` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `ordering` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '999',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name`(`name`, `type`, `category_tags_id`, `custom_link`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 38 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for gm_sliders
-- ----------------------------
DROP TABLE IF EXISTS `gm_sliders`;
CREATE TABLE `gm_sliders`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `category_tags_id` int(11) NOT NULL,
  `ordering` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `type`(`type`, `category_tags_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for gm_tags
-- ----------------------------
DROP TABLE IF EXISTS `gm_tags`;
CREATE TABLE `gm_tags`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `footer_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `is_last_rewrite` tinyint(1) NOT NULL DEFAULT 0,
  `is_rewrited` tinyint(1) NOT NULL DEFAULT 0,
  `show_home` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `url`(`url`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 465 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for gm_theme
-- ----------------------------
DROP TABLE IF EXISTS `gm_theme`;
CREATE TABLE `gm_theme`  (
  `theme_id` int(11) NOT NULL AUTO_INCREMENT,
  `theme_class` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`theme_id`) USING BTREE,
  UNIQUE INDEX `theme_class`(`theme_class`) USING BTREE,
  UNIQUE INDEX `theme_class_3`(`theme_class`) USING BTREE,
  INDEX `theme_class_2`(`theme_class`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8 COLLATE = utf8_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for gm_users
-- ----------------------------
DROP TABLE IF EXISTS `gm_users`;
CREATE TABLE `gm_users`  (
  `user_id` int(11) NOT NULL,
  `gender` enum('1','2') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '1',
  `about` text CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  UNIQUE INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_unicode_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
