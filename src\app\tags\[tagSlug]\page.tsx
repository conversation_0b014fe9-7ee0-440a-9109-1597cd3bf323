"use client";

import { useState, useEffect, useCallback } from "react";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { getGamesByTag, Game, getTags, normalizeImagePath } from "@/lib/apiService";
import Link from "next/link";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";

// Define API response interface
interface ApiResponse {
  status: string;
  data: Game[];
  total: number;
}

// Games per page
const GAMES_PER_PAGE = 20;

export default function TagPage({ params }: { params: { tagSlug: string } }) {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Get current page from URL, default to 1
  const currentPage = Number(searchParams.get('page') || '1');
  
  const [games, setGames] = useState<Game[]>([]);
  const [tagName, setTagName] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalGames, setTotalGames] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [currentTagSlug, setCurrentTagSlug] = useState("");

  // 在Next.js 15中需要通过await获取参数
  const tagSlugValue = useCallback(async () => {
    return (await params).tagSlug;
  }, [params]);
  
  // Helper function: Format slug into readable tag name
  function formatTagName(slug: string): string {
    return slug.charAt(0).toUpperCase() + slug.slice(1).replace(/-/g, ' ');
  }

  // 使用useEffect处理异步params
  useEffect(() => {
    const loadTagData = async () => {
      try {
        const tagSlug = await tagSlugValue();
        setCurrentTagSlug(tagSlug);
        setIsLoading(true);
        
        // Calculate offset for pagination
        const offset = (currentPage - 1) * GAMES_PER_PAGE;
        
        console.log(`Fetching games for tag: ${tagSlug}, page: ${currentPage}, offset: ${offset}`);
        
        // Try to get data from API
        const result = await getGamesByTag(tagSlug, GAMES_PER_PAGE, offset);
        console.log("API result:", result);
        
        if (result.status === 'success' && result.data && result.data.length > 0) {
          // API returned data successfully
          console.log(`Successfully loaded ${result.data.length} games from API`);
          setGames(result.data);
          
          // Set total games count and total pages
          const total = result.total || result.data.length;
          setTotalGames(total);
          setTotalPages(Math.ceil(total / GAMES_PER_PAGE));
          
          // Extract tag name from first game, or use formatted slug
          if (result.data[0].tags_name) {
            // Try to find the exact tag from tags_name
            const tagsArray = result.data[0].tags_name.split(',').map(t => t.trim());
            const tagMatch = tagsArray.find(t => 
              t.toLowerCase() === tagSlug.toLowerCase() ||
              t.toLowerCase().replace(/-/g, ' ') === tagSlug.toLowerCase().replace(/-/g, ' ')
            );
            
            if (tagMatch) {
              setTagName(tagMatch);
            } else {
              setTagName(formatTagName(tagSlug));
            }
          } else {
            // Try to fetch tag name from all tags
            try {
              const tagsResult = await getTags();
              if (tagsResult.status === 'success') {
                const matchingTag = tagsResult.data.find((tag: any) => 
                  tag.url === tagSlug || 
                  tag.url.toLowerCase() === tagSlug.toLowerCase()
                );
                
                if (matchingTag) {
                  setTagName(matchingTag.name);
                } else {
                  setTagName(formatTagName(tagSlug));
                }
              } else {
                setTagName(formatTagName(tagSlug));
              }
            } catch (error) {
              setTagName(formatTagName(tagSlug));
            }
          }
          
          setError(null);
        } else {
          // API didn't return any game data
          console.log(`No games found from API for tag: ${tagSlug}`);
          setError(`No games found with this tag: ${formatTagName(tagSlug)}`);
          setTagName(formatTagName(tagSlug));
          setGames([]);
          setTotalPages(1);
        }
      } catch (err) {
        console.error("Error fetching tag games:", err);
        setError(`Failed to load games. Please try again later.`);
        setTagName(formatTagName(tagSlug));
        setGames([]);
        setTotalPages(1);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadTagData();
  }, [tagSlugValue, currentPage]);

  // Handle page navigation
  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages) return;
    
    // Use router.push to update URL with new page number
    router.push(`/tags/${currentTagSlug}?page=${page}`);
  };

  // Generate pagination buttons
  const renderPagination = () => {
    // Don't show pagination if only one page
    if (totalPages <= 1) return null;

    // Determine which page numbers to display
    const pageNumbers = [];
    // Always show first page
    pageNumbers.push(1);
    
    // Show page numbers around current page
    let start = Math.max(2, currentPage - 2);
    let end = Math.min(totalPages - 1, currentPage + 2);
    
    // If start page is not 2, add ellipsis
    if (start > 2) {
      pageNumbers.push('...'); 
    }
    
    // Add middle page numbers
    for (let i = start; i <= end; i++) {
      pageNumbers.push(i);
    }
    
    // If end page is not second-to-last, add ellipsis
    if (end < totalPages - 1) {
      pageNumbers.push('...');
    }
    
    // If total pages is greater than 1, always show last page
    if (totalPages > 1) {
      pageNumbers.push(totalPages);
    }

    return (
      <div className="flex flex-col items-center mt-12 mb-8">
        <div className="flex justify-center items-center space-x-2 bg-white p-3 rounded-lg shadow-md">
          {/* Previous page button */}
          <button 
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className={`px-4 py-2 rounded-md transition-all duration-200 font-medium ${
              currentPage === 1 
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                : 'bg-blue-100 text-blue-700 hover:bg-blue-200 hover:scale-105'
            }`}
            aria-label="Previous page"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          
          {/* Page number buttons */}
          {pageNumbers.map((page, index) => (
            page === '...' ? (
              <span key={`ellipsis-${index}`} className="px-3 py-2 text-gray-500">...</span>
            ) : (
              <button
                key={`page-${page}`}
                onClick={() => handlePageChange(Number(page))}
                className={`px-4 py-2 rounded-md transition-all duration-200 ${
                  Number(page) === currentPage 
                    ? 'bg-blue-600 text-white font-bold scale-110' 
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-700 hover:scale-105'
                }`}
              >
                {page}
              </button>
            )
          ))}
          
          {/* Next page button */}
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className={`px-4 py-2 rounded-md transition-all duration-200 font-medium ${
              currentPage === totalPages 
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                : 'bg-blue-100 text-blue-700 hover:bg-blue-200 hover:scale-105'
            }`}
            aria-label="Next page"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <Header />
      <main className="flex-grow container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Tag Header */}
          <div className="relative mb-10 bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg overflow-hidden">
            <div className="absolute inset-0 opacity-10 bg-pattern"></div>
            <div className="relative z-10 px-6 py-10 md:px-10 text-white">
              <div className="flex flex-col md:flex-row md:items-center justify-between">
                <div>
                  <div className="flex items-center mb-4">
                    <Link href="/tags" className="text-blue-200 hover:text-white transition-colors mr-2 flex items-center">
                      <svg 
                        className="w-4 h-4 mr-1" 
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                      </svg>
                      Tags
                    </Link>
                    <span className="text-blue-300 mx-1">/</span>
                    <h1 className="text-2xl md:text-3xl font-bold">{tagName} Games</h1>
                  </div>
                  <p className="text-blue-100 max-w-2xl">
                    Explore our collection of {totalGames} {tagName.toLowerCase()} games. Play for free on any device right in your browser.
                  </p>
                </div>
              </div>
            </div>
          </div>
          
          {isLoading ? (
            <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 md:gap-6">
              {Array.from({ length: GAMES_PER_PAGE }).map((_, index) => (
                <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
                  <div className="aspect-[4/3] bg-blue-100"></div>
                  <div className="p-3 md:p-4">
                    <div className="h-5 bg-blue-100 rounded-full w-3/4 mb-3"></div>
                    <div className="h-4 bg-gray-100 rounded-full w-1/2 mb-2"></div>
                    <div className="flex justify-between mt-3">
                      <div className="h-6 w-16 bg-blue-50 rounded-full"></div>
                      <div className="h-6 w-6 bg-green-50 rounded-full"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-16 bg-white rounded-xl shadow-md p-8 max-w-2xl mx-auto">
              <div className="bg-red-50 p-6 rounded-lg mb-6">
                <svg className="w-16 h-16 text-red-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h2 className="text-2xl font-bold text-red-600 mb-2">No Games Found</h2>
                <p className="text-gray-600 mb-6">{error}</p>
              </div>
              <Link
                href="/tags"
                className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-8 rounded-lg transition-colors inline-flex items-center group"
              >
                <svg className="w-5 h-5 mr-2 group-hover:animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16l-4-4m0 0l4-4m-4 4h18" />
                </svg>
                Browse All Tags
              </Link>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 md:gap-6">
                {games.map((game) => (
                  <Link key={game.game_id || game.game_name} href={`/games/${game.game_name}`}>
                    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:scale-105 h-full flex flex-col">
                      <div className="relative aspect-[4/3] overflow-hidden">
                        <Image
                          src={normalizeImagePath(game.image)}
                          alt={game.name}
                          fill
                          className="object-cover hover:scale-110 transition-transform duration-500"
                          sizes="(max-width: 640px) 45vw, (max-width: 768px) 30vw, (max-width: 1024px) 25vw, 20vw"
                        />
                        {game.rating && (
                          <div className="absolute top-2 right-2 bg-green-600 text-white text-xs font-medium px-1.5 md:px-2 py-0.5 md:py-1 rounded-md flex items-center">
                            <svg className="w-3 h-3 mr-0.5 text-yellow-300" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            {game.rating}
                          </div>
                        )}
                      </div>
                      <div className="p-3 md:p-4 flex-grow flex flex-col">
                        <h3 className="font-semibold text-gray-900 mb-1 truncate text-sm md:text-base">{game.name}</h3>
                        <div className="text-xs mb-2 flex-grow hidden sm:block">
                          {game.category_name && (
                            <span className="bg-blue-50 text-blue-600 px-2 py-0.5 rounded-full text-xs">
                              {game.category_name}
                            </span>
                          )}
                        </div>
                        <div className="flex items-center justify-between mt-auto">
                          <span className="text-blue-600 text-xs sm:text-sm font-medium inline-flex items-center group">
                            Play
                            <svg className="w-3 h-3 sm:w-4 sm:h-4 ml-1 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                            </svg>
                          </span>
                          <div className="bg-blue-50 text-blue-700 p-1 rounded-full">
                            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
              
              {/* Pagination controls */}
              {renderPagination()}
            </>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
} 