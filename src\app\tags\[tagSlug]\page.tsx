import { getTags } from "@/lib/apiService";
import { Suspense } from "react";
import TagPageClient from "./TagPageClient";

interface TagParams {
  params: Promise<{
    tagSlug: string;
  }>;
}

// 生成静态参数用于预渲染标签页面
export async function generateStaticParams() {
  try {
    // 获取所有标签
    const tagsResult = await getTags();

    if (tagsResult.status === 'success' && tagsResult.data) {
      // 转换为参数格式
      return tagsResult.data.map((tag: any) => ({
        tagSlug: tag.url || tag.name.toLowerCase().replace(/\s+/g, '-'),
      }));
    }

    // 如果获取标签失败，返回一些常见的标签作为备用
    return [
      { tagSlug: 'action' },
      { tagSlug: 'adventure' },
      { tagSlug: 'puzzle' },
      { tagSlug: 'racing' },
      { tagSlug: 'sports' },
    ];
  } catch (error) {
    console.error('Error generating static params for tag pages:', error);
    // 返回基本的标签列表作为备用
    return [
      { tagSlug: 'action' },
      { tagSlug: 'adventure' },
      { tagSlug: 'puzzle' },
      { tagSlug: 'racing' },
      { tagSlug: 'sports' },
    ];
  }
}

export default async function TagPage({ params }: TagParams) {
  // 确保先解析params参数
  const resolvedParams = await params;
  const tagSlug = resolvedParams.tagSlug;

  return (
    <Suspense fallback={
      <div className="flex flex-col min-h-screen">
        <div className="flex-grow container mx-auto px-4 py-8">
          <div className="max-w-7xl mx-auto">
            <div className="mb-8">
              <div className="h-8 bg-gray-200 rounded w-1/3 mb-2 animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded w-1/4 animate-pulse"></div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
              {Array.from({ length: 20 }).map((_, index) => (
                <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
                  <div className="w-full h-48 bg-gray-200"></div>
                  <div className="p-4">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    }>
      <TagPageClient tagSlug={tagSlug} />
    </Suspense>
  );
}