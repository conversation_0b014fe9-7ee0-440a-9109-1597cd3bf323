import { getTags } from "@/lib/apiService";
import TagPageClient from "./TagPageClient";

interface TagParams {
  params: Promise<{
    tagSlug: string;
  }>;
}

// 生成静态参数用于预渲染标签页面
export async function generateStaticParams() {
  try {
    // 获取所有标签
    const tagsResult = await getTags();

    if (tagsResult.status === 'success' && tagsResult.data) {
      // 转换为参数格式
      return tagsResult.data.map((tag: any) => ({
        tagSlug: tag.url || tag.name.toLowerCase().replace(/\s+/g, '-'),
      }));
    }

    // 如果获取标签失败，返回一些常见的标签作为备用
    return [
      { tagSlug: 'action' },
      { tagSlug: 'adventure' },
      { tagSlug: 'puzzle' },
      { tagSlug: 'racing' },
      { tagSlug: 'sports' },
    ];
  } catch (error) {
    console.error('Error generating static params for tag pages:', error);
    // 返回基本的标签列表作为备用
    return [
      { tagSlug: 'action' },
      { tagSlug: 'adventure' },
      { tagSlug: 'puzzle' },
      { tagSlug: 'racing' },
      { tagSlug: 'sports' },
    ];
  }
}

export default async function TagPage({ params }: TagParams) {
  // 确保先解析params参数
  const resolvedParams = await params;
  const tagSlug = resolvedParams.tagSlug;

  return (
    <TagPageClient tagSlug={tagSlug} />
  );
}