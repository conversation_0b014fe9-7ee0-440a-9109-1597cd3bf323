import type { <PERSON>ada<PERSON> } from "next";
import { Header } from "@/components/layout/Header";

export const metadata: Metadata = {
  title: "Search Games - Drive Mad",
  description: "Search and explore thousands of fun games on Drive Mad.",
};

export default function SearchLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen">
      {children}
    </div>
  );
} 