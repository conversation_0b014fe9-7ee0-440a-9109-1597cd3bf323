"use client";

import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useState, useEffect, useRef } from "react";
import { Logo } from "./Logo";
import Image from "next/image";
import { getCategories, Category, getFavoritedGames, processImagePath } from "@/lib/apiService";

const navigationLinks = [
  {
    href: "/top-games",
    label: "TOP GAMES",
  },
  {
    href: "/favorites",
    label: "FAVORITE GAMES",
  },
  {
    href: "/leaderboards",
    label: "LEADERBOARD",
  },
  {
    href: "/categories",
    label: "CATEGORIES",
    dropdown: true,
  },
];

export function Header() {
  const pathname = usePathname();
  const router = useRouter();
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [favoritedGamesCount, setFavoritedGamesCount] = useState(0);
  
  // 添加一个ref来引用下拉菜单容器
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const updateFavoriteCount = () => {
      if (typeof window !== 'undefined') {
        const favoritedGames = getFavoritedGames();
        setFavoritedGamesCount(favoritedGames.length);
      }
    };

    updateFavoriteCount();

    window.addEventListener('storage', updateFavoriteCount);

    const intervalId = setInterval(updateFavoriteCount, 5000);

    return () => {
      window.removeEventListener('storage', updateFavoriteCount);
      clearInterval(intervalId);
    };
  }, []);

  // 添加点击外部关闭下拉菜单的效果
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    }

    // 添加全局点击事件监听器
    document.addEventListener("mousedown", handleClickOutside);
    
    // 清理事件监听器
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // 获取分类数据
  useEffect(() => {
    async function fetchCategories() {
      try {
        console.log('[Header] Starting to fetch categories...');
        setIsLoadingCategories(true);
        const result = await getCategories();
        
        console.log('[Header] Category API response:', result);
        
        if (result.status === 'success' && result.data && result.data.length > 0) {
          console.log(`[Header] Successfully loaded ${result.data.length} categories`);
          console.log('[Header] Category names:', result.data.map((cat: any) => cat.name).join(', '));
          setCategories(result.data);
        } else {
          console.error("[Header] Failed to load categories:", result.message || "Unknown error");
          // 当API返回错误时，设置空分类列表
          setCategories([]);
        }
      } catch (error) {
        console.error("[Header] Error fetching categories:", error);
        setCategories([]);
      } finally {
        setIsLoadingCategories(false);
      }
    }

    fetchCategories();
  }, []);

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
      setSearchQuery("");
    }
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const toggleDropdown = (e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡
    setIsDropdownOpen(!isDropdownOpen);
  };

  return (
    <header className="bg-blue-600 text-white py-3">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Link href="/" className="flex items-center mr-6">
              <div className="font-extrabold text-2xl bg-clip-text text-transparent bg-gradient-to-r from-white to-blue-200">
                <span className="text-white">D</span>
                <span>rive</span>
                <span className="text-white ml-1">Mad</span>
              </div>
            </Link>

            <nav className="hidden lg:flex items-center space-x-6">
              {/* <Link href="/new-releases" className="nav-link">New Games</Link> */}
              
              <div className="relative" ref={dropdownRef}>
                <button 
                  className="nav-link flex items-center"
                  onClick={toggleDropdown}
                >
                  Categories
                  <svg 
                    className={`ml-1 w-4 h-4 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                
                {isDropdownOpen && (
                  <div className="absolute top-full left-0 mt-1 w-48 bg-white rounded-md shadow-lg z-50">
                    <div className="py-1 grid grid-cols-1 gap-1">
                      {isLoadingCategories ? (
                        <div className="px-4 py-2 text-gray-500">Loading...</div>
                      ) : categories.length > 0 ? (
                        <>
                          {categories.slice(0, 8).map((category) => (
                            <Link 
                              key={category.id} 
                              href={`/categories/${category.category_pilot}`} 
                              className="dropdown-item hover:bg-gray-100 px-4 py-2 text-sm text-gray-700 block"
                              onClick={() => setIsDropdownOpen(false)}
                            >
                              {category.name}
                            </Link>
                          ))}
                          <Link 
                            href="/categories" 
                            className="dropdown-item font-semibold hover:bg-gray-100 px-4 py-2 text-sm text-gray-700 block"
                            onClick={() => setIsDropdownOpen(false)}
                          >
                            All Categories
                          </Link>
                        </>
                      ) : (
                        <div className="px-4 py-2 text-gray-500">No categories available</div>
                      )}
                    </div>
                  </div>
                )}
              </div>
              
              <Link href="/pages/about" className="nav-link">About</Link>
              <Link href="/pages/privacy" className="nav-link">Privacy</Link>
            </nav>
          </div>

          <div className="flex items-center space-x-2 md:space-x-4">
            <Link href="/recent-games" className="hidden sm:flex items-center justify-center w-9 h-9 rounded-full bg-blue-500 hover:bg-blue-700 transition-colors">
              <svg 
                className="w-5 h-5" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth="2" 
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" 
                />
              </svg>
              <span className="sr-only">Recent Games</span>
            </Link>
            
            <Link 
              href="/favorites" 
              className="hidden sm:flex items-center justify-center w-9 h-9 rounded-full bg-blue-500 hover:bg-blue-700 transition-colors relative"
            >
              <svg 
                className="w-5 h-5" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth="2" 
                  d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" 
                />
              </svg>
              <span className="sr-only">Favorite Games</span>
            </Link>

            <div className="flex-shrink-0 md:w-60 w-36 sm:w-44">
              <form onSubmit={handleSearchSubmit} className="relative">
                <input
                  type="text"
                  placeholder="Search games..."
                  className="w-full py-1 md:py-1.5 pl-2 md:pl-3 pr-6 md:pr-8 rounded-full text-xs md:text-sm text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <button
                  type="submit"
                  className="absolute right-1.5 md:right-2 top-1/2 transform -translate-y-1/2 text-gray-500"
                >
                  <svg
                    className="w-3 h-3 md:w-4 md:h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </button>
              </form>
            </div>

            <button
              className="flex items-center justify-center w-8 h-8 rounded-full focus:outline-none"
              onClick={toggleMenu}
            >
              {isMenuOpen ? (
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              ) : (
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              )}
            </button>
          </div>
        </div>

        {isMenuOpen && (
          <nav className="lg:hidden mt-4 pb-2">
            <div className="bg-blue-700 rounded-lg overflow-hidden">
              <div className="py-2">
                <Link href="/new-releases" className="block py-3 px-6 hover:bg-blue-800 transition-colors">
                  New Games
                </Link>
                <Link href="/categories" className="block py-3 px-6 hover:bg-blue-800 transition-colors">
                  Categories
                </Link>
                
                {/* 分隔线 */}
                <div className="border-t border-blue-500 mx-4 my-2"></div>
                
                <div className="grid grid-cols-2 gap-0">
                  <Link href="/recent-games" className="flex items-center py-3 px-6 hover:bg-blue-800 transition-colors">
                    <svg 
                      className="w-5 h-5 mr-2 flex-shrink-0" 
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path 
                        strokeLinecap="round" 
                        strokeLinejoin="round" 
                        strokeWidth="2" 
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" 
                      />
                    </svg>
                    <span className="whitespace-nowrap">Recent Games</span>
                  </Link>
                  
                  <Link href="/favorites" className="flex items-center py-3 px-6 hover:bg-blue-800 transition-colors">
                    <svg 
                      className="w-5 h-5 mr-2 flex-shrink-0" 
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path 
                        strokeLinecap="round" 
                        strokeLinejoin="round" 
                        strokeWidth="2" 
                        d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" 
                      />
                    </svg>
                    <span className="whitespace-nowrap">Favorite Games</span>
                  </Link>
                </div>
                
                {/* 分隔线 */}
                <div className="border-t border-blue-500 mx-4 my-2"></div>
                
                <Link href="/pages/about" className="block py-3 px-6 hover:bg-blue-800 transition-colors">
                  About
                </Link>
                <Link href="/pages/privacy" className="block py-3 px-6 hover:bg-blue-800 transition-colors">
                  Privacy
                </Link>
              </div>
            </div>
          </nav>
        )}
      </div>
    </header>
  );
}
