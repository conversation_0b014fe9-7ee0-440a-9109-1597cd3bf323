"use client";

import Image from "next/image";
import Link from "next/link";
import { processImagePath } from "@/lib/apiService";

export interface GameCardProps {
  id: string;
  title: string;
  imageUrl: string;
  category: string;
  plays: number;
  size?: "small" | "medium" | "large";
}

export function GameCard({ id, title, imageUrl, category, plays, size = "medium" }: GameCardProps) {
  const sizeClasses = {
    small: "w-full",
    medium: "w-full",
    large: "w-full",
  };

  const imageSize = {
    small: 150,
    medium: 180,
    large: 220,
  };

  return (
    <Link href={`/games/${id}`} className={`game-card ${sizeClasses[size]} block h-full`}>
      <div className="flex flex-col h-full">
        <div className="relative bg-blue-100 rounded-t-md overflow-hidden" style={{ aspectRatio: "1/1" }}>
          <Image
            src={processImagePath(imageUrl)}
            alt={title}
            fill
            className="w-full h-full object-cover"
          />
        </div>
        <div className="p-2 bg-white rounded-b-md flex-1 flex flex-col">
          <h3 className="text-gray-800 font-medium text-sm truncate">{title}</h3>
          <div className="flex justify-between items-center mt-1">
            <span className="text-xs text-gray-600">{category}</span>
          </div>
        </div>
      </div>
    </Link>
  );
}

export function FeaturedGameCard({ id, title, imageUrl, category }: Omit<GameCardProps, "plays" | "size">) {
  return (
    <Link href={`/games/${id}`} className="relative w-full h-full block">
      <div className="relative w-full h-full">
        <Image
          src={processImagePath(imageUrl)}
          alt={title}
          fill
          className="w-full h-full object-cover rounded-md"
        />
        <div className="absolute bottom-0 left-0 w-full p-4 bg-gradient-to-t from-black/80 to-transparent rounded-b-md">
          <h3 className="text-white font-bold text-lg">{title}</h3>
          <div className="flex items-center justify-between">
            <span className="text-white/80 text-sm">{category}</span>
            <button className="play-now-button mt-2">
              PLAY NOW
            </button>
          </div>
        </div>
      </div>
    </Link>
  );
}
