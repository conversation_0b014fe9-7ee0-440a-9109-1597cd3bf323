import { Metadata } from "next";

export default function TagLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>{children}</>
  );
}

interface TagParams {
  params: {
    tagSlug: string;
  };
}

export async function generateMetadata({ params }: TagParams): Promise<Metadata> {
  // 从URL中获取标签名称
  const tagSlug = params.tagSlug;
  
  // 将slug格式化为可读名称
  const formattedName = formatTagName(tagSlug);
  
  return {
    title: `${formattedName} Games - Play Tagged Games Online | Drive Mad`,
    description: `Play the best ${formattedName.toLowerCase()} games online at Drive Mad. Explore our collection of games tagged as ${formattedName.toLowerCase()} with amazing physics-based challenges and puzzles.`,
    keywords: `${formattedName.toLowerCase()} games, drive mad ${formattedName.toLowerCase()}, online ${formattedName.toLowerCase()} games, free ${formattedName.toLowerCase()} games, tagged games`,
    openGraph: {
      title: `${formattedName} Games - Drive Mad`,
      description: `Discover our collection of games tagged as ${formattedName.toLowerCase()}. Enjoy challenging physics-based driving games and more.`,
      type: 'website',
      url: `/tags/${tagSlug}`,
    },
  };
}

// 辅助函数：将slug格式化为可读的标签名称
function formatTagName(slug: string): string {
  return slug
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
} 