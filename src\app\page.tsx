"use client";

import { useState, useEffect, useRef } from "react";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { FeaturedGamesSlider } from "@/components/game-slider/FeaturedGamesSlider";
import { GameSection } from "@/components/game-slider/GameSection";
import { GameSeries } from "@/components/game-slider/GameSeries";
import { getFeaturedGames, getHomeCategories, getLatestGames, getPopularGames, getHomeTags, Game, Category, Tag, getPageDescription, getGameDetails, processImagePath } from "@/lib/apiService";
import { FeaturedGamesBox } from "@/components/featured/FeaturedGamesBox";
import Image from "next/image";
import Link from "next/link";

// 添加HTML内容处理函数
function sanitizeHtml(html: string): string {
  if (!html) return '';
  
  // 替换转义的HTML字符
  return html
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/&amp;/g, '&');
}

// 定义首页分类项的接口
interface CategoryItem {
  id: string;
  title: string;
  imageUrl: string;
}

export default function Home() {
  const [sliderGames, setSliderGames] = useState([]);
  const [categories, setCategories] = useState<CategoryItem[]>([]);
  const [latestGames, setLatestGames] = useState([]);
  const [popularGames, setPopularGames] = useState([]);
  const [homeTags, setHomeTags] = useState<Tag[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCategoriesLoading, setIsCategoriesLoading] = useState(true);
  const [isLatestGamesLoading, setIsLatestGamesLoading] = useState(true);
  const [isPopularGamesLoading, setIsPopularGamesLoading] = useState(true);
  const [isTagsLoading, setIsTagsLoading] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [featuredGame, setFeaturedGame] = useState<Game | null>(null);
  const [isFeaturedGameLoading, setIsFeaturedGameLoading] = useState(true);

  // 添加页面描述状态
  const [pageDescription, setPageDescription] = useState({
    title: "Drive Mad - Master the Art of Physics-Based Driving Challenges",
    shortDesc: "Drive through impossible terrains, overcome gravity-defying obstacles, and test your skills in over 100 exciting levels.",
    content: "Drive Mad brings you the ultimate physics-based driving experience where precision and timing are key to conquering each challenge. Navigate through dangerous terrains, perform incredible stunts, and push the limits of physics in this addictive driving game. Perfect for quick gaming sessions or hours of entertainment, Drive Mad combines simple controls with complex challenges that will keep you coming back for more.",
    sections: [
      {
        title: "Experience the Thrill of Physics-Based Driving",
        content: "With intuitive controls that are easy to learn but difficult to master, Drive Mad offers a unique driving experience unlike any other game. Use W, D, X, or arrow keys to drive forward, and S, A, Z, or left/down arrows to reverse. Adapt your driving style to different vehicles and terrains as you progress through increasingly challenging levels."
      },
      {
        title: "Endless Fun with Community-Created Content",
        content: "Beyond the main game, explore countless player-created levels that add new challenges and creative twists to the Drive Mad experience. Each level brings a fresh perspective and unique obstacles to overcome, ensuring the gameplay never gets stale. Join the community and discover why millions of players can't get enough of Drive Mad."
      },
      {
        title: "Play Anywhere on Any Device",
        content: "Enjoy Drive Mad seamlessly across all your devices. Whether you're on a desktop computer, tablet, or mobile phone, the game adapts perfectly to your screen size and control preferences. No downloads required - jump right into the action directly from your browser and save your progress as you go."
      }
    ]
  });
  const [isDescriptionLoading, setIsDescriptionLoading] = useState(true);

  // 添加全屏切换函数
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      // 进入全屏模式
      const iframe = iframeRef.current;
      if (iframe && iframe.requestFullscreen) {
        iframe.requestFullscreen().catch(err => {
          console.error(`Error attempting to enable fullscreen: ${err.message}`);
        });
      }
    } else {
      // 退出全屏模式
      if (document.exitFullscreen) {
        document.exitFullscreen().catch(err => {
          console.error(`Error attempting to exit fullscreen: ${err.message}`);
        });
      }
    }
  };

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  useEffect(() => {
    async function fetchSliderGames() {
      try {
        const result = await getFeaturedGames(10); // 从API获取10个游戏，确保有足够数据
        if (result.status === 'success' && result.data.length > 0) {
          // 转换API数据格式为轮播组件所需格式
          const formattedGames = result.data.map((game: Game) => ({
            id: game.game_name,
            title: game.name,
            imageUrl: game.image,
            category: game.category_name,
          }));
          setSliderGames(formattedGames);
        }
      } catch (error) {
        console.error('Failed to fetch slider games:', error);
      } finally {
        setIsLoading(false);
      }
    }

    async function fetchHomeCategories() {
      try {
        const result = await getHomeCategories();
        if (result.status === 'success' && result.data.length > 0) {
          // 只使用show_home=1的分类
          const homeCategories = result.data
            .filter((category: any) => category.show_home === 1)
            .map((category: any) => ({
              id: category.category_pilot,
              title: category.name,
              // 修复图片路径，确保包含/images/cat/
              imageUrl: processImagePath(category.image, category.category_pilot),
            }));
          setCategories(homeCategories);
        }
      } catch (error) {
        console.error('Failed to fetch home categories:', error);
      } finally {
        setIsCategoriesLoading(false);
      }
    }

    async function fetchLatestGames() {
      try {
        setIsLatestGamesLoading(true);
        const result = await getLatestGames(24);
        if (result.status === 'success' && result.data.length > 0) {
          // 转换API数据格式为GameSection组件所需格式
          const formattedGames = result.data.map((game: Game) => ({
            id: game.game_name,
            title: game.name,
            imageUrl: game.image,
            category: game.category_name,
            plays: game.plays,
          }));
          setLatestGames(formattedGames);
        }
      } catch (error) {
        console.error('Failed to fetch latest games:', error);
      } finally {
        setIsLatestGamesLoading(false);
      }
    }

    async function fetchPopularGames() {
      try {
        setIsPopularGamesLoading(true);
        const result = await getPopularGames(12);
        if (result.status === 'success' && result.data.length > 0) {
          // 转换API数据格式为GameSection组件所需格式
          const formattedGames = result.data.map((game: Game) => ({
            id: game.game_name,
            title: game.name,
            imageUrl: game.image,
            category: game.category_name,
            plays: game.plays,
          }));
          setPopularGames(formattedGames);
        }
      } catch (error) {
        console.error('Failed to fetch popular games:', error);
      } finally {
        setIsPopularGamesLoading(false);
      }
    }

    async function fetchHomeTags() {
      try {
        setIsTagsLoading(true);
        const result = await getHomeTags(20); // 最多获取20个标签
        if (result.status === 'success' && result.data.length > 0) {
          setHomeTags(result.data);
        }
      } catch (error) {
        console.error('Failed to fetch home tags:', error);
      } finally {
        setIsTagsLoading(false);
      }
    }

    // 添加获取页面描述的函数
    async function fetchPageDescription() {
      try {
        setIsDescriptionLoading(true);
        const result = await getPageDescription("/");
        
        if (result.status === "success") {
          // 解析content_value中的内容，这里假设是JSON格式
          let parsedContent = {
            title: result.data.description || "Play the Best Online Games on Drive Mad",
            shortDesc: "",
            content: "",
            sections: []
          };
          
          try {
            if (result.data.has_content === "1" && result.data.content_value) {
              // 尝试解析JSON内容
              const contentData = JSON.parse(result.data.content_value);
              parsedContent = {
                title: contentData.title || result.data.description,
                shortDesc: "",
                content: contentData.content || "",
                sections: contentData.sections || []
              };
            }
          } catch (parseError) {
            // 解析失败时使用content_value作为普通文本
            parsedContent.content = result.data.content_value;
          }
          
          setPageDescription(parsedContent);
        }
      } catch (error) {
        console.error("Error fetching page description:", error);
      } finally {
        setIsDescriptionLoading(false);
      }
    }

    // 获取特定游戏数据
    async function fetchFeaturedGame() {
      try {
        setIsFeaturedGameLoading(true);
        const result = await getGameDetails('drive-mad');
        
        if (result.status === 'success' && result.data) {
          setFeaturedGame(result.data);
        }
      } catch (error) {
        console.error('Failed to fetch featured game:', error);
      } finally {
        setIsFeaturedGameLoading(false);
      }
    }

    fetchSliderGames();
    fetchHomeCategories();
    fetchLatestGames();
    fetchPopularGames();
    fetchHomeTags();
    fetchPageDescription();
    fetchFeaturedGame();
  }, []);

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow container mx-auto px-4 py-6">
        {/* Main content area - Game and Featured Games */}
        <div className="flex flex-col lg:flex-row gap-6 mb-8">
          {/* Left: First 6 Top Games */}
          <div className="lg:w-1/6 order-2 lg:order-1">
            <div className="bg-blue-600 rounded-md p-3 flex flex-col h-full">
              <div className="grid grid-cols-1 gap-3 flex-grow">
                {isPopularGamesLoading ? (
                  // Loading skeleton
                  Array.from({ length: 6 }).map((_, index) => (
                    <div key={index} className="bg-blue-700/40 rounded-md overflow-hidden h-[120px] animate-pulse"></div>
                  ))
                ) : (
                  // First 6 Top games
                  popularGames.slice(0, 6).map((game: any, index) => (
                    <Link key={`left-${game.id}-${index}`} href={`/games/${game.id}`} className="block">
                      <div className="bg-blue-700/20 rounded-md overflow-hidden h-[120px] relative hover:bg-blue-700/40 transition-colors">
                        <Image
                          src={game.imageUrl}
                          alt={game.title}
                          fill
                          className="object-cover rounded-md"
                        />
                      </div>
                    </Link>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Center: Game iframe */}
          <div className="lg:w-2/3 order-1 lg:order-2">
            <div className="relative h-[500px] md:h-[800px] rounded-lg overflow-hidden shadow-md">
              {isFeaturedGameLoading ? (
                <div className="w-full h-full flex items-center justify-center bg-gray-800">
                  <div className="w-16 h-16 border-t-4 border-b-4 border-blue-500 rounded-full animate-spin"></div>
                </div>
              ) : featuredGame ? (
                <>
                  {/* 游戏iframe */}
                  <iframe 
                    ref={iframeRef}
                    src={featuredGame.file} 
                    className="w-full h-full border-0" 
                    sandbox="allow-same-origin allow-scripts allow-popups allow-forms allow-presentation allow-top-navigation"
                    allow="fullscreen; autoplay; clipboard-write; cross-origin-isolated" 
                    referrerPolicy="no-referrer"
                    title={featuredGame.name}
                    style={{ display: 'block' }}
                  ></iframe>
                  
                  {/* 游戏信息区域 - 移到顶部并简化 */}
                  <div className="absolute top-0 left-0 right-0 p-2 bg-black/30 backdrop-blur-[2px]">
                    <div className="flex justify-between items-center">
                      <h3 className="text-white font-bold text-lg md:text-xl">{featuredGame.name}</h3>
                      
                      {/* 全屏按钮 - 只显示图标 */}
                      <button 
                        onClick={toggleFullscreen}
                        className="p-1.5 rounded-md hover:bg-white/10 transition-colors"
                        aria-label={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </>
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gray-800 text-white">
                  <p>Game not available</p>
                </div>
              )}
            </div>
          </div>

          {/* Right: Next 6 Top Games */}
          <div className="lg:w-1/6 order-3">
            <div className="bg-blue-600 rounded-md p-3 flex flex-col h-full">
              <div className="grid grid-cols-1 gap-3 flex-grow">
                {isPopularGamesLoading ? (
                  // Loading skeleton
                  Array.from({ length: 6 }).map((_, index) => (
                    <div key={index} className="bg-blue-700/40 rounded-md overflow-hidden h-[120px] animate-pulse"></div>
                  ))
                ) : (
                  // Next 6 Top games (索引6-11)
                  popularGames.slice(6, 12).map((game: any, index) => (
                    <Link key={`right-${game.id}-${index}`} href={`/games/${game.id}`} className="block">
                      <div className="bg-blue-700/20 rounded-md overflow-hidden h-[120px] relative hover:bg-blue-700/40 transition-colors">
                        <Image
                          src={game.imageUrl}
                          alt={game.title}
                          fill
                          className="object-cover rounded-md"
                        />
                      </div>
                    </Link>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Game Series - 使用从API获取的分类数据 */}
        {isCategoriesLoading ? (
          <div className="mb-8">
            <h2 className="section-title mb-4">Categories</h2>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2">
              {Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="bg-blue-600 rounded-md p-2 flex items-center h-[80px] animate-pulse">
                  <div className="w-[50px] h-[50px] bg-blue-400 rounded-md"></div>
                  <div className="ml-2 w-20 h-4 bg-blue-400 rounded-md"></div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              <h2 className="section-title">
                Categories
              </h2>
              <Link
                href="/categories"
                className="view-all-button"
              >
                View All
              </Link>
            </div>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2">
              {categories.slice(0, 6).map((item) => (
                <Link
                  key={item.id}
                  href={`/categories/${item.id}`}
                  className="block"
                >
                  <div className="bg-blue-600 rounded-md p-2 flex items-center h-[80px] group hover:bg-blue-700 transition-colors">
                    <div className="relative w-[50px] h-[50px] flex-shrink-0">
                      <Image
                        src={item.imageUrl}
                        alt={item.title}
                        width={50}
                        height={50}
                        className="object-contain"
                      />
                    </div>
                    <div className="ml-2 text-white text-sm truncate">{item.title}</div>
                    <div className="ml-auto">
                      <div className="bg-green-500 text-white h-6 w-6 flex items-center justify-center rounded-md">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 5l7 7-7 7"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        )}

        {/* New Releases */}
        {isLatestGamesLoading ? (
          <div className="mb-8">
            <h2 className="section-title mb-4">New Games</h2>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-6 gap-3">
              {Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="bg-white rounded-lg overflow-hidden shadow-md animate-pulse">
                  <div className="bg-blue-200" style={{ aspectRatio: "1/1" }}></div>
                  <div className="p-3">
                    <div className="h-5 bg-gray-200 rounded mb-2"></div>
                    <div className="flex justify-between items-center">
                      <div className="h-4 w-16 bg-blue-100 rounded-full"></div>
                      <div className="h-4 w-12 bg-gray-200 rounded"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <GameSection
            title="New Games"
            games={latestGames}
            viewAllLink="/new-releases"
          />
        )}

        {/* 游戏标签区域 */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <h2 className="section-title">
              Tags
            </h2>
            <Link
              href="/tags"
              className="view-all-button"
            >
              View All
            </Link>
          </div>
          
          {isTagsLoading ? (
            <div className="flex flex-wrap gap-2">
              {Array.from({ length: 12 }).map((_, index) => (
                <div key={index} className="h-10 w-20 md:w-28 bg-blue-100 rounded-full animate-pulse"></div>
              ))}
            </div>
          ) : (
            <div className="flex flex-wrap gap-2 md:gap-3">
              {homeTags.map((tag) => (
                <Link 
                  key={tag.id} 
                  href={`/tags/${tag.url}`}
                  className="bg-blue-100 hover:bg-blue-200 text-blue-700 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 hover:shadow-md"
                >
                  {tag.name}
                </Link>
              ))}
            </div>
          )}
        </div>

        {/* SEO Text Area */}
        <div className="mt-8 py-6 px-6 bg-white rounded-md shadow-md text-gray-800">
          {isDescriptionLoading ? (
            // 加载状态
            <div className="animate-pulse">
              <div className="h-6 bg-gray-200 rounded w-3/4 mb-3"></div>
              <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-3/4 mb-3"></div>
              
              <div className="h-4 bg-gray-200 rounded w-1/2 mt-4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-full mb-3"></div>
              
              <div className="h-4 bg-gray-200 rounded w-1/2 mt-4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
            </div>
          ) : (
            <>
              <h2 className="text-base font-medium mb-2" dangerouslySetInnerHTML={{ __html: sanitizeHtml(pageDescription.title) }} />
              <div className="text-xs leading-relaxed mb-3 text-gray-700 font-normal" dangerouslySetInnerHTML={{ __html: sanitizeHtml(pageDescription.content) }} />
              
              {pageDescription.sections.map((section, index) => (
                <div key={index} className="mt-3">
                  <h3 className="text-sm font-medium mb-1" dangerouslySetInnerHTML={{ __html: sanitizeHtml(section.title) }} />
                  <div className="text-xs leading-relaxed mb-2 text-gray-700 font-normal" dangerouslySetInnerHTML={{ __html: sanitizeHtml(section.content) }} />
                </div>
              ))}
            </>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
}
