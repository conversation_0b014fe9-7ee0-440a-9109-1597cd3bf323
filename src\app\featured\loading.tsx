import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";

export default function Loading() {
  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <Header />
      <main className="flex-grow container mx-auto px-4 py-8">
        {/* Hero Section Skeleton */}
        <div className="bg-blue-600 text-white p-8 rounded-lg shadow-md mb-8">
          <div className="h-10 w-64 bg-blue-500 rounded animate-pulse mb-4"></div>
          <div className="h-5 w-full max-w-3xl bg-blue-500 rounded animate-pulse"></div>
        </div>

        {/* Games Grid Skeleton */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-5">
          {Array.from({ length: 18 }).map((_, index) => (
            <div key={index} className="bg-white rounded-lg overflow-hidden shadow-md animate-pulse">
              <div className="bg-blue-200" style={{ aspectRatio: "1/1" }}></div>
              <div className="p-3">
                <div className="h-5 bg-gray-200 rounded mb-2"></div>
                <div className="flex justify-between items-center">
                  <div className="h-4 w-16 bg-blue-100 rounded-full"></div>
                  <div className="h-4 w-12 bg-gray-200 rounded"></div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Pagination Skeleton */}
        <div className="flex justify-center mt-8 mb-4">
          <div className="flex items-center space-x-2">
            <div className="w-20 h-8 bg-blue-200 rounded-md animate-pulse"></div>
            <div className="flex space-x-2">
              {Array.from({ length: 5 }).map((_, index) => (
                <div key={index} className="w-8 h-8 bg-blue-200 rounded-md animate-pulse"></div>
              ))}
            </div>
            <div className="w-20 h-8 bg-blue-200 rounded-md animate-pulse"></div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
} 