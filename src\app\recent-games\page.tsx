"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { getRecentGames, RecentGame } from "@/lib/apiService";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";

export default function RecentGamesPage() {
  const [recentGames, setRecentGames] = useState<RecentGame[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    function loadRecentGames() {
      try {
        setIsLoading(true);
        const games = getRecentGames();
        setRecentGames(games);
      } catch (error) {
        console.error("Error loading recent games:", error);
      } finally {
        setIsLoading(false);
      }
    }

    loadRecentGames();
    
    // 监听最近游戏变化
    const handleRecentGamesUpdate = () => {
      loadRecentGames();
    };
    
    // 使用事件分发的方式监听localStorage变化
    window.addEventListener('storage', handleRecentGamesUpdate);
    
    // 添加自定义事件监听器
    window.addEventListener('recentGamesUpdated', handleRecentGamesUpdate);
    
    return () => {
      window.removeEventListener('storage', handleRecentGamesUpdate);
      window.removeEventListener('recentGamesUpdated', handleRecentGamesUpdate);
    };
  }, []);

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <Header />
      
      <main className="flex-grow container mx-auto px-4 py-8">
        <div className="mb-8 bg-white p-6 rounded-lg shadow-sm">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Recently Played Games</h1>
          <p className="text-gray-700">
            Games you've played recently will appear here
          </p>
        </div>
        
        {isLoading ? (
          // 加载状态显示骨架屏
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 md:gap-6">
            {Array.from({ length: 12 }).map((_, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm overflow-hidden animate-pulse">
                <div className="aspect-square w-full bg-gray-200"></div>
                <div className="p-3">
                  <div className="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        ) : recentGames.length > 0 ? (
          // 显示最近游戏
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 md:gap-6">
            {recentGames.map((game) => (
              <Link 
                key={game.game_id} 
                href={`/games/${game.game_name}`}
                className="flex flex-col bg-white rounded-lg shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden group transform hover:-translate-y-1 h-full"
              >
                <div className="aspect-square relative overflow-hidden">
                  <Image
                    src={game.image}
                    alt={game.name}
                    fill
                    sizes="(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 16vw"
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                    priority={false}
                  />
                  {/* 添加游戏卡片上的播放图标 */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center">
                    <div className="opacity-0 group-hover:opacity-100 transform group-hover:scale-100 scale-50 transition-all duration-300">
                      <div className="w-12 h-12 rounded-full bg-blue-600 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="p-3 flex flex-col flex-grow">
                  <h3 className="font-medium text-base text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors min-h-[40px] leading-tight">
                    {game.name}
                  </h3>
                  <div className="mt-auto flex items-center">
                    {game.rating && (
                      <div className="flex items-center text-yellow-600">
                        <svg className="w-4 h-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <span className="text-xs font-medium">{game.rating}</span>
                      </div>
                    )}
                  </div>
                </div>
              </Link>
            ))}
          </div>
        ) : (
          // 没有最近游戏时显示空状态
          <div className="text-center py-16 bg-white rounded-lg shadow-sm">
            <div className="max-w-md mx-auto">
              <svg 
                className="w-16 h-16 mx-auto mb-4 text-blue-500" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth="1.5" 
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" 
                />
              </svg>
              <h2 className="text-xl font-bold text-gray-900 mb-2">No recently played games</h2>
              <p className="text-gray-700 mb-6">
                Games that you've played will appear here so you can quickly find them again.
              </p>
              <Link 
                href="/" 
                className="inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-md transition-colors"
              >
                Browse Games
                <svg 
                  className="ml-2 w-4 h-4" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth="2" 
                    d="M9 5l7 7-7 7" 
                  />
                </svg>
              </Link>
            </div>
          </div>
        )}
      </main>
      
      <Footer />
    </div>
  );
} 