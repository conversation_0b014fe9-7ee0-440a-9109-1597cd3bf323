"use client";

import { useState, useEffect } from "react";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { GameCard } from "@/components/game-card/GameCard";
import { getPopularGames, Game } from "@/lib/apiService";

export default function TopFavoriteGamesPage() {
  const [games, setGames] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const itemsPerPage = 24;

  useEffect(() => {
    fetchGames(currentPage);
  }, [currentPage]);

  async function fetchGames(pageNum: number) {
    try {
      setIsLoading(true);
      const result = await getPopularGames(itemsPerPage, (pageNum - 1) * itemsPerPage);
      
      if (result.status === 'success') {
        const formattedGames = result.data.map((game: Game) => ({
          id: game.game_name,
          title: game.name,
          imageUrl: game.image,
          category: game.category_name,
          plays: game.plays,
        }));
        
        setGames(formattedGames);
        
        // Calculate total pages
        if (result.total) {
          setTotalPages(Math.ceil(result.total / itemsPerPage));
        } else {
          setTotalPages(Math.ceil(formattedGames.length > 0 ? 2 : 1));
        }
      }
    } catch (error) {
      console.error('Error fetching popular games:', error);
    } finally {
      setIsLoading(false);
    }
  }

  function handlePageChange(page: number) {
    window.scrollTo(0, 0);
    setCurrentPage(page);
  }

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8 text-center">Top Favorite Games</h1>
        
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {games.map((game) => (
            <div key={`${currentPage}-${game.id}`} className="w-full h-full flex flex-col">
              <GameCard
                id={game.id}
                title={game.title}
                imageUrl={game.imageUrl}
                category={game.category}
                plays={game.plays}
                size="small"
              />
            </div>
          ))}
          
          {isLoading && games.length === 0 && (
            Array.from({ length: 12 }).map((_, index) => (
              <div key={`skeleton-${index}`} className="bg-white rounded-lg overflow-hidden shadow-md animate-pulse">
                <div className="bg-blue-200" style={{ aspectRatio: "1/1" }}></div>
                <div className="p-3">
                  <div className="h-5 bg-gray-200 rounded mb-2"></div>
                  <div className="h-4 w-16 bg-blue-100 rounded-full"></div>
                </div>
              </div>
            ))
          )}
        </div>
        
        {/* Pagination */}
        {games.length > 0 && (
          <div className="mt-8 flex justify-center">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1 || isLoading}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md disabled:opacity-50 transition-colors"
              >
                Previous
              </button>
              
              <div className="flex space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }).map((_, index) => {
                  // Logic to show a window of 5 pages around the current page
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = index + 1;
                  } else if (currentPage <= 3) {
                    pageNum = index + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + index;
                  } else {
                    pageNum = currentPage - 2 + index;
                  }
                  
                  return (
                    <button
                      key={`page-${pageNum}`}
                      onClick={() => handlePageChange(pageNum)}
                      className={`w-10 h-10 flex items-center justify-center rounded-md ${
                        currentPage === pageNum 
                          ? 'bg-blue-700 text-white' 
                          : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
                
                {totalPages > 5 && currentPage < totalPages - 2 && (
                  <>
                    <span className="px-1 self-end">...</span>
                    <button
                      onClick={() => handlePageChange(totalPages)}
                      className="w-10 h-10 flex items-center justify-center rounded-md bg-blue-100 text-blue-700 hover:bg-blue-200"
                    >
                      {totalPages}
                    </button>
                  </>
                )}
              </div>
              
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages || isLoading}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md disabled:opacity-50 transition-colors"
              >
                Next
              </button>
            </div>
          </div>
        )}
        
        {games.length === 0 && !isLoading && (
          <div className="text-center py-12">
            <h3 className="text-xl font-medium text-gray-600">No games found</h3>
            <p className="text-gray-500 mt-2">Check back later for popular games</p>
          </div>
        )}
      </main>
      <Footer />
    </div>
  );
} 