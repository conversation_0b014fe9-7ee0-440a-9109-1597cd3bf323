"use client";

import { useState } from "react";
import Link from "next/link";
import { GameCard } from "../game-card/GameCard";
import type { GameCardProps } from "../game-card/GameCard";

interface GameSectionProps {
  title: string;
  games: GameCardProps[];
  viewAllLink: string;
}

export function GameSection({ title, games, viewAllLink }: GameSectionProps) {
  const [currentPage, setCurrentPage] = useState(0);
  
  const itemsPerPage = 12;
  const totalPages = Math.ceil(games.length / itemsPerPage);
  
  // 计算当前页要显示的游戏
  const startIndex = currentPage * itemsPerPage;
  const visibleGames = games.slice(startIndex, startIndex + itemsPerPage);

  const handlePageChange = (newPage: number) => {
    if (newPage >= 0 && newPage < totalPages) {
      setCurrentPage(newPage);
    }
  };

  // 确定是否应该显示分页控件
  const shouldShowPagination = totalPages > 1;

  return (
    <div className="mb-8">
      <div className="flex flex-wrap sm:flex-nowrap justify-between items-center mb-4 gap-1">
        <h2 className="section-title mb-0">{title}</h2>
        <div className="flex items-center space-x-1 sm:space-x-2">
          {shouldShowPagination && (
            <>
              <div className="section-pagination mr-1 sm:mr-2">
                {Array.from({ length: totalPages }).map((_, index) => (
                  <div 
                    key={index} 
                    className={`${index === currentPage ? "pagination-dot-active" : "pagination-dot"} mx-0.5 cursor-pointer`}
                    onClick={() => handlePageChange(index)}
                  />
                ))}
              </div>
              <div className="flex">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  className={`bg-yellow-500 text-white h-5 w-5 sm:h-6 sm:w-6 flex items-center justify-center rounded-l-md ${currentPage === 0 ? 'opacity-50 cursor-not-allowed' : ''}`}
                  disabled={currentPage === 0}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-3 w-3 sm:h-4 sm:w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 19l-7-7 7-7"
                    />
                  </svg>
                </button>
                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  className={`bg-yellow-500 text-white h-5 w-5 sm:h-6 sm:w-6 flex items-center justify-center rounded-r-md ${currentPage === totalPages - 1 ? 'opacity-50 cursor-not-allowed' : ''}`}
                  disabled={currentPage === totalPages - 1}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-3 w-3 sm:h-4 sm:w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </button>
              </div>
            </>
          )}
          <Link
            href={viewAllLink}
            className="view-all-button whitespace-nowrap text-center"
          >
            View All
          </Link>
        </div>
      </div>
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
        {visibleGames.map((game) => (
          <div key={game.id} className="w-full h-full flex flex-col">
            <GameCard
              id={game.id}
              title={game.title}
              imageUrl={game.imageUrl}
              category={game.category}
              plays={game.plays}
              size="small"
            />
          </div>
        ))}
      </div>
    </div>
  );
}
