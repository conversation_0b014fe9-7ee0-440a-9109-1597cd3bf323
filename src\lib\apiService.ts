const API_BASE_URL = "https://api.drivemad.store";
// 备用API端点
const BACKUP_API_BASE_URL = "https://api.drivemad.store";
const USE_PROXY = false; // 静态导出模式下不能使用本地API代理

// 代理URL生成辅助函数
function getProxyUrl(targetUrl: string): string {
  if (!USE_PROXY) return targetUrl;

  try {
    // 检查URL是否有效
    if (!targetUrl || typeof targetUrl !== 'string') {
      throw new Error('Invalid URL: empty or not a string');
    }

    // 确保URL有协议前缀
    let processedUrl = targetUrl;
    if (!processedUrl.startsWith('http://') && !processedUrl.startsWith('https://')) {
      processedUrl = `https://${processedUrl}`;
    }

    // 检查是否为有效URL格式
    try {
      new URL(processedUrl);
    } catch (urlError) {
      console.error("Invalid URL format:", processedUrl);
      throw urlError;
    }

    // 直接编码整个URL
    const encodedUrl = encodeURIComponent(processedUrl);
    return `/api/proxy?url=${encodedUrl}`;
  } catch (error) {
    console.error("Error creating proxy URL:", targetUrl, error);

    // 返回错误代理URL
    return `/api/proxy?error=invalid_url&original=${encodeURIComponent(String(targetUrl || 'undefined'))}`;
  }
}

export interface Game {
  game_id: number;
  game_name: string;
  name: string;
  image: string;
  plays?: number;
  rating?: string;
  description?: string;
  file?: string;
  featured?: string;
  category_name?: string;
  tags_name?: string;
  tags?: { id: number; name: string; url: string }[];
  date_added?: string;
  mobile?: string;
  w?: number;
  h?: number;
  like_count?: number;
  favorite_count?: number;
}

export interface SearchResult {
  status: string;
  data: Game[];
  total: number;
}

export interface SearchParams {
  keyword: string;
  category_id?: number;
  tag_id?: number;
  min_rating?: number;
  max_rating?: number;
  skip?: number;
  limit?: number;
}

export interface Category {
  id: number;
  name: string;
  category_pilot: string;
  image: string;
  show_home: number;
}

export interface Tag {
  id: number;
  name: string;
  url: string;
  show_home: number;
}

// 默认分类数据（已不再使用，保留注释以供参考）
// export const DEFAULT_CATEGORIES = [
//   { id: 1, name: 'Action', slug: 'action', category_pilot: 'action-games', image: '' },
//   { id: 2, name: 'Adventure', slug: 'adventure', category_pilot: 'adventure-games', image: '' },
//   { id: 3, name: 'Puzzle', slug: 'puzzle', category_pilot: 'puzzle-games', image: '' },
//   { id: 4, name: 'Racing', slug: 'racing', category_pilot: 'racing-games', image: '' },
//   { id: 5, name: 'Sports', slug: 'sports', category_pilot: 'sports-games', image: '' },
//   { id: 6, name: 'Strategy', slug: 'strategy', category_pilot: 'strategy-games', image: '' },
//   { id: 7, name: 'Shooting', slug: 'shooting', category_pilot: 'shooting-games', image: '' },
//   { id: 8, name: 'Simulation', slug: 'simulation', category_pilot: 'simulation-games', image: '' },
//   { id: 9, name: 'RPG', slug: 'rpg', category_pilot: 'rpg-games', image: '' },
//   { id: 10, name: 'Educational', slug: 'educational', category_pilot: 'educational-games', image: '' }
// ];

// 默认标签数据（用作备用）
export const DEFAULT_TAGS = [
  { id: 1, name: 'New', slug: 'new' },
  { id: 2, name: 'Popular', slug: 'popular' },
  { id: 3, name: 'Featured', slug: 'featured' },
  { id: 4, name: 'Hot', slug: 'hot' },
  { id: 5, name: 'Best', slug: 'best' },
  { id: 6, name: 'Top', slug: 'top' },
  { id: 7, name: 'Trending', slug: 'trending' },
  { id: 8, name: 'Recommended', slug: 'recommended' }
];

// 工具函数：为URL自动拼接appname=drivemadstore参数
function appendAppNameToUrl(url: string): string {
  if (url.includes("appname=")) return url;
  return url + (url.includes("?") ? "&" : "?") + "appname=drivemadstore";
}

// 直接调用API函数（绕过代理）
async function directFetch(url: string, options = {}, timeout = 5000) {
  const controller = new AbortController();
  const { signal } = controller;
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  // 确保URL包含appname参数，但避免重复添加
  let processedUrl = url.includes('appname=') ? url : appendAppNameToUrl(url);

  try {
    console.log(`[API] Direct fetching: ${processedUrl}`);

    const fetchOptions: RequestInit = {
      ...options,
      signal,
      cache: 'no-cache' as RequestCache,
      headers: {
        ...((options as any).headers || {}) as Record<string, string>,
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json',
      }
    };

    let response;
    // 首先尝试主API端点
    let primaryUrl: string;
    try {
      // 如果URL已经包含完整域名，不做修改
      primaryUrl = url.includes("://") ?
        processedUrl :
        `${API_BASE_URL}${url.startsWith('/') ? url : '/' + url}`;

      // 确保URL包含appname参数，但避免重复添加
      const finalPrimaryUrl = primaryUrl.includes('appname=') ? primaryUrl : appendAppNameToUrl(primaryUrl);
      console.log(`[API] Direct trying primary API endpoint: ${finalPrimaryUrl}`);

      response = await fetch(finalPrimaryUrl, fetchOptions);
      if (!response.ok) {
        const errorMsg = `Primary API endpoint returned ${response.status} for URL: ${finalPrimaryUrl}`;
        console.error(errorMsg);
        if (response.status === 404) {
          throw new Error(`404 Not Found: ${finalPrimaryUrl}`);
        }
        throw new Error(errorMsg);
      }
    } catch (primaryError: any) {
      console.log(`[API] Direct primary API failed: ${primaryError?.message || 'Unknown error'}, trying backup...`);

      // 如果主API端点失败，尝试备用API端点
      let backupUrl: string;
      backupUrl = url.includes("://") ?
        processedUrl :
        `${BACKUP_API_BASE_URL}${url.startsWith('/') ? url : '/' + url}`;

      // 确保URL包含appname参数，但避免重复添加
      const finalBackupUrl = backupUrl.includes('appname=') ? backupUrl : appendAppNameToUrl(backupUrl);
      console.log(`[API] Direct trying backup API endpoint: ${finalBackupUrl}`);

      response = await fetch(finalBackupUrl, fetchOptions);
      if (!response.ok) {
        const errorMsg = `Backup API endpoint returned ${response.status} for URL: ${finalBackupUrl}`;
        console.error(errorMsg);
        throw new Error(
          `Both API endpoints failed for path "${url}": ` +
          `Primary (${API_BASE_URL}): ${primaryError?.message || 'Unknown error'}, ` +
          `Backup (${BACKUP_API_BASE_URL}) returned ${response.status}`
        );
      }
    }

    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    console.error('[API] Direct fetch error:', error);
    throw error;
  }
}

// 带超时的fetch函数
async function fetchWithTimeout(url: string, options = {}, timeout = 5000) {
  // 创建一个AbortController以便能够中止请求
  const controller = new AbortController();
  const { signal } = controller;

  // 设置超时
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    // 确保URL包含appname参数，但避免重复添加
    const urlWithAppName = url.includes('appname=') ? url : appendAppNameToUrl(url);

    // 处理URL - 如果开启了代理模式，使用代理URL
    let processedUrl = USE_PROXY ?
      getProxyUrl(urlWithAppName) :
      urlWithAppName;

    // 检查代理URL是否包含错误标记
    if (processedUrl.includes('error=invalid_url')) {
      clearTimeout(timeoutId);
      console.error('Invalid URL detected in request:', url);
      throw new Error(`Invalid URL format: ${url}`);
    }

    // 确保代理URL是绝对URL
    if (processedUrl.startsWith('/')) {
      // 在浏览器环境中
      if (typeof window !== 'undefined') {
        // 使用当前域名作为基础
        processedUrl = `${window.location.origin}${processedUrl}`;
      } else {
        // 在服务器环境中，必须使用完整URL
        // Next.js服务器环境中，我们需要明确指定API基础URL
        processedUrl = `http://localhost:${process.env.PORT || 3000}${processedUrl}`;
      }
    }

    console.log(`[API] Fetching: ${processedUrl}`);

    try {
      // 尝试执行fetch请求
      const response = await fetch(processedUrl, {
        ...options,
        signal,
        // 添加重试和缓存策略
        cache: 'no-cache', // 不使用缓存
      });

      clearTimeout(timeoutId);

      // 检查HTTP状态码
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}, URL: ${url}`);
      }

      return response;
    } catch (fetchError: any) {
      if (USE_PROXY && !url.includes('localhost') &&
          (options as any)?.method !== 'POST') { // 对POST请求不要自动转为直接请求，因为代理已支持POST
        // 如果代理请求失败，尝试直接请求 - 但仅限于GET请求
        console.log(`[API] Proxy request failed (${fetchError.message}) for URL: ${url}, trying direct request...`);
        clearTimeout(timeoutId); // 清除之前的超时

        // 直接调用API（绕过代理）
        return directFetch(urlWithAppName, options, timeout);
      }
      throw new Error(`Fetch error for URL ${url}: ${fetchError.message}`);
    }
  } catch (error: any) {
    clearTimeout(timeoutId);

    // 更详细的错误日志
    if (error.name === 'AbortError') {
      console.error(`[API] Request timed out after ${timeout}ms:`, url);
      throw new Error(`Request timeout (${timeout}ms) for URL: ${url}`);
    } else if (error.message && error.message.includes('Failed to parse URL')) {
      console.error('[API] URL parsing error:', url, error);
      throw new Error(`URL parsing error for: ${url}, Details: ${error.message}`);
    } else if (error.message && error.message.includes('fetch failed')) {
      console.error('[API] Network fetch failed:', url, error);
      throw new Error(`Network request failed for: ${url}, Details: ${error.message}`);
    } else {
      console.error('[API] Request error:', error, 'URL:', url);
      throw error;
    }
  }
}

// 尝试使用多个备选API端点
async function tryApiEndpoints(urlPath: string, options = {}, timeout = 8000) {
  // 确保URL路径前面有斜杠
  const path = urlPath.startsWith('/') ? urlPath : `/${urlPath}`;

  // 确保URL包含appname参数，但避免重复添加
  const pathWithAppName = path.includes('appname=') ? path : appendAppNameToUrl(path);

  // 首先尝试主API端点
  try {
    const primaryUrl = `${API_BASE_URL}${pathWithAppName}`;
    console.log(`[API] Trying primary API endpoint: ${primaryUrl}`);

    const response = await fetchWithTimeout(primaryUrl, options, timeout);
    if (!response.ok) {
      if (response.status === 404) {
        console.error(`[API] 404 Not Found for path: "${path}" at ${API_BASE_URL}`);
        throw new Error(`404 Not Found for path: ${path} at ${API_BASE_URL}`);
      }
      throw new Error(`API returned ${response.status} for path: ${path} at ${API_BASE_URL}`);
    }
    return response;
  } catch (error: any) {
    console.error(`[API] Primary API endpoint failed for path "${path}": ${error?.message || 'Unknown error'}`);

    // 如果主API端点失败，尝试备用API端点
    try {
      const backupUrl = `${BACKUP_API_BASE_URL}${pathWithAppName}`;
      console.log(`[API] Trying backup API endpoint: ${backupUrl}`);

      const response = await fetchWithTimeout(backupUrl, options, timeout);
      if (!response.ok) {
        if (response.status === 404) {
          console.error(`[API] 404 Not Found for path: "${path}" at ${BACKUP_API_BASE_URL}`);
          throw new Error(`404 Not Found for path: ${path} at ${BACKUP_API_BASE_URL}`);
        }
        throw new Error(`API returned ${response.status} for path: ${path} at ${BACKUP_API_BASE_URL}`);
      }
      return response;
    } catch (backupError: any) {
      console.error(`[API] Backup API endpoint also failed for path "${path}": ${backupError?.message || 'Unknown error'}`);
      throw new Error(
        `All API endpoints failed for path "${path}": ` +
        `Primary (${API_BASE_URL}): ${error?.message || 'Unknown primary error'}, ` +
        `Backup (${BACKUP_API_BASE_URL}): ${backupError?.message || 'Unknown backup error'}`
      );
    }
  }
}

// 获取推荐游戏列表
export async function getFeaturedGames(limit = 10) {
  try {
    const response = await tryApiEndpoints(
      `/games/featured?limit=${limit}`
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch featured games: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching featured games:", error);
    // 返回空结果
    return {
      status: "error",
      data: [],
      total: 0
    };
  }
}

// 获取首页显示的分类
export async function getHomeCategories() {
  try {
    const response = await tryApiEndpoints(
      `/categories/home`
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch home categories: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching home categories:", error);
    // 返回错误状态和空数据，而不是使用默认分类
    return {
      status: "error",
      message: error instanceof Error ? error.message : "Unknown error fetching home categories",
      data: []
    };
  }
}

export async function searchGames(params: SearchParams): Promise<SearchResult> {
  try {
    const queryParams = new URLSearchParams();

    // 添加必需参数
    queryParams.append("keyword", params.keyword);

    // 添加可选参数
    if (params.category_id) queryParams.append("category_id", params.category_id.toString());
    if (params.tag_id) queryParams.append("tag_id", params.tag_id.toString());
    if (params.min_rating) queryParams.append("min_rating", params.min_rating.toString());
    if (params.max_rating) queryParams.append("max_rating", params.max_rating.toString());
    if (params.skip) queryParams.append("skip", params.skip.toString());
    if (params.limit) queryParams.append("limit", params.limit.toString());

    // 使用带超时的fetch
    const response = await tryApiEndpoints(
      `/games/search?${queryParams.toString()}`
    );

    if (!response.ok) {
      throw new Error(`Search request failed: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error searching games:", error);
    // 返回一个空的搜索结果
    return {
      status: "error",
      data: [],
      total: 0
    };
  }
}

// 获取分类列表
export async function getCategories() {
  try {
    console.log('[API] Fetching categories...');

    // 在构建时直接调用外部API
    if (typeof window === 'undefined') {
      // 服务器端/构建时直接调用外部API
      const directApiUrl = `${API_BASE_URL}/categories?appname=drivemadstore`;
      console.log(`[BUILD] Direct API call to: ${directApiUrl}`);

      try {
        const response = await fetch(directApiUrl, {
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
          },
          cache: 'force-cache' // 静态导出模式下使用force-cache
        });

        if (response.ok) {
          const data = await response.json();
          if (data && data.data && Array.isArray(data.data)) {
            console.log(`[BUILD] Direct API received ${data.data.length} categories`);
            return data;
          }
        }
      } catch (buildError) {
        console.error('[BUILD] Direct API call failed for categories:', buildError);
      }
    }

    // 客户端或API路由调用
    const apiPath = '/categories';
    console.log(`[API] Using API path: ${apiPath}`);

    const response = await tryApiEndpoints(apiPath, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    if (!response.ok) {
      console.warn(`[API] Categories API returned ${response.status}`);
      throw new Error(`Failed to fetch categories: ${response.status}`);
    }

    const data = await response.json();
    // 记录API返回的数据
    console.log('[API] Successfully fetched categories, data:', data);

    // 检查返回的数据格式
    if (data && data.data && Array.isArray(data.data)) {
      console.log(`[API] Got ${data.data.length} categories from API`);
      console.log('[API] Category names:', data.data.map((cat: any) => cat.name).join(', '));
    } else {
      console.warn('[API] Unexpected data format returned from categories API:', data);
    }

    return data;
  } catch (error) {
    console.error("[API] Error fetching categories:", error);

    // 返回错误状态和空数据，而不是使用默认分类
    return {
      status: "error",
      message: error instanceof Error ? error.message : "Unknown error fetching categories",
      data: []
    };
  }
}

// 获取标签列表
export async function getTags() {
  try {
    console.log('Fetching tags from API...');

    // 在构建时直接调用外部API
    if (typeof window === 'undefined') {
      // 服务器端/构建时直接调用外部API
      const directApiUrl = `${API_BASE_URL}/tags?appname=drivemadstore`;
      console.log(`[BUILD] Direct API call to: ${directApiUrl}`);

      try {
        const response = await fetch(directApiUrl, {
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
          },
          cache: 'force-cache' // 静态导出模式下使用force-cache
        });

        if (response.ok) {
          const data = await response.json();
          if (data.status === 'success' && Array.isArray(data.data)) {
            // 添加url属性（如果不存在）
            const processedData = data.data.map((tag: any) => ({
              id: tag.id,
              name: tag.name,
              url: tag.url || tag.name.toLowerCase().replace(/\s+/g, '-'),
              show_home: tag.show_home || 0
            }));

            console.log(`[BUILD] Direct API received ${processedData.length} tags`);
            return {
              status: 'success',
              data: processedData
            };
          }
        }
      } catch (buildError) {
        console.error('[BUILD] Direct API call failed for tags:', buildError);
      }
    }

    // 客户端或API路由调用
    const response = await tryApiEndpoints(
      `/tags`,
      {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      },
      10000 // 10秒超时，延长时间允许API响应
    );

    if (!response.ok) {
      console.error(`Failed to fetch tags with status: ${response.status}`);
      throw new Error(`Failed to fetch tags: ${response.status}`);
    }

    const data = await response.json();
    console.log('Tags data fetched successfully:', data);

    // 确保返回的数据格式正确
    if (data.status === 'success' && Array.isArray(data.data)) {
      // 添加url属性（如果不存在）
      const processedData = data.data.map((tag: any) => ({
        id: tag.id,
        name: tag.name,
        url: tag.url || tag.name.toLowerCase().replace(/\s+/g, '-'),
        show_home: tag.show_home || 0
      }));

      return {
        status: 'success',
        data: processedData
      };
    }

    return data;
  } catch (error) {
    console.error("Error fetching tags:", error);

    // 尝试从其他API端点获取标签数据作为备选
    try {
      console.log('Trying to fetch tags from homepage tags endpoint...');
      const homeTagsResponse = await tryApiEndpoints(
        `/tags/home`,
        {
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
          }
        },
        8000
      );

      if (homeTagsResponse.ok) {
        const homeTagsData = await homeTagsResponse.json();
        console.log('Home tags fetched as fallback:', homeTagsData);
        if (homeTagsData.status === 'success' && Array.isArray(homeTagsData.data)) {
          return homeTagsData;
        }
      }
    } catch (fallbackError) {
      console.error("Error fetching home tags as fallback:", fallbackError);
    }

    // 构造更有用的默认标签数据
    const enhancedDefaultTags = [
      { id: 1, name: 'New', url: 'new', show_home: 1 },
      { id: 2, name: 'Popular', url: 'popular', show_home: 1 },
      { id: 3, name: 'Featured', url: 'featured', show_home: 1 },
      { id: 4, name: 'Hot', url: 'hot', show_home: 1 },
      { id: 5, name: 'Best', url: 'best', show_home: 1 },
      { id: 6, name: 'Top', url: 'top', show_home: 1 },
      { id: 7, name: 'Trending', url: 'trending', show_home: 1 },
      { id: 8, name: 'Recommended', url: 'recommended', show_home: 1 },
      { id: 9, name: 'Action', url: 'action', show_home: 0 },
      { id: 10, name: 'Adventure', url: 'adventure', show_home: 0 },
      { id: 11, name: 'Puzzle', url: 'puzzle', show_home: 0 },
      { id: 12, name: 'Racing', url: 'racing', show_home: 0 },
      { id: 13, name: 'Sports', url: 'sports', show_home: 0 },
      { id: 14, name: 'Strategy', url: 'strategy', show_home: 0 },
      { id: 15, name: 'Shooting', url: 'shooting', show_home: 0 },
      { id: 16, name: 'Arcade', url: 'arcade', show_home: 0 },
      { id: 17, name: 'Casual', url: 'casual', show_home: 0 },
      { id: 18, name: 'Multiplayer', url: 'multiplayer', show_home: 0 },
      { id: 19, name: '2D', url: '2d', show_home: 0 },
      { id: 20, name: '3D', url: '3d', show_home: 0 }
    ];

    // 在API请求失败时返回更多样化的标签数据
    console.log('Returning enhanced default tags as last resort');
    return {
      status: "success",
      data: enhancedDefaultTags
    };
  }
}

// 获取首页显示的标签
export async function getHomeTags(limit = 20) {
  try {
    // 使用带超时的fetch
    const response = await tryApiEndpoints(
      `/tags/home${limit ? `?limit=${limit}` : ''}`
    );

    if (!response.ok) {
      // 如果特定的首页标签API失败，尝试获取所有标签并过滤
      const allTagsResponse = await getTags();
      if (allTagsResponse.status === 'success' && allTagsResponse.data) {
        // 过滤显示在首页的标签
        const homeTagsData = allTagsResponse.data
          .filter((tag: Tag) => tag.show_home === 1)
          .slice(0, limit);

        return {
          status: "success",
          data: homeTagsData
        };
      }
      throw new Error(`Failed to fetch home tags: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching home tags:", error);
    // 在API请求失败时，返回过滤后的默认标签（假设所有默认标签都可以在首页显示）
    return {
      status: "success",
      data: DEFAULT_TAGS.slice(0, limit)
    };
  }
}

// 获取最新游戏列表
export async function getLatestGames(limit = 10, offset = 0) {
  try {
    // 计算页码（API使用page参数而不是offset）
    const page = Math.floor(offset / limit) + 1;
    console.log(`Fetching latest games with limit=${limit}, offset=${offset}, calculated page=${page}`);

    // 在构建时直接调用外部API
    if (typeof window === 'undefined') {
      // 服务器端/构建时直接调用外部API
      const directApiUrl = `${API_BASE_URL}/games/latest?limit=${limit}&page=${page}&appname=drivemadstore`;
      console.log(`[BUILD] Direct API call to: ${directApiUrl}`);

      try {
        const response = await fetch(directApiUrl, {
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
          },
          cache: 'force-cache' // 静态导出模式下使用force-cache
        });

        if (response.ok) {
          const data = await response.json();
          if (data && data.data) {
            console.log(`[BUILD] Direct API received ${data.data.length} games`);
            return data;
          }
        }
      } catch (buildError) {
        console.error('[BUILD] Direct API call failed:', buildError);
      }
    }

    // 客户端或API路由调用
    const apiUrl = `/games/latest?limit=${limit}&page=${page}&_t=${Date.now()}`;
    console.log(`[API] getLatestGames using URL: ${apiUrl}`);

    const response = await tryApiEndpoints(
      apiUrl,
      {
        // 设置请求头，禁用缓存
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch latest games: ${response.status}`);
    }

    const data = await response.json();

    // 记录返回的数据数量，帮助调试
    if (data && data.data) {
      console.log(`[API] getLatestGames received ${data.data.length} games for page ${page}`);

      // 记录前三个游戏的ID和名称，帮助验证数据是否随页码变化
      if (data.data.length > 0) {
        console.log(`[API] Page ${page} first games:`,
          data.data.slice(0, Math.min(3, data.data.length)).map((g: Game) =>
            `${g.game_id}:${g.name}`
          )
        );
      }
    }

    return data;
  } catch (error) {
    return handleAPIError("Latest games fetch failed", error, generateDefaultGames("latest", limit));
  }
}

// 获取最热门游戏列表
export async function getPopularGames(limit = 10, offset = 0) {
  try {
    // 计算页码（API使用page参数而不是offset）
    const page = Math.floor(offset / limit) + 1;
    console.log(`Fetching popular games with limit=${limit}, offset=${offset}, calculated page=${page}`);

    // 在构建时直接调用外部API
    if (typeof window === 'undefined') {
      // 服务器端/构建时直接调用外部API
      const directApiUrl = `${API_BASE_URL}/games/popular?limit=${limit}&page=${page}&appname=drivemadstore`;
      console.log(`[BUILD] Direct API call to: ${directApiUrl}`);

      try {
        const response = await fetch(directApiUrl, {
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
          },
          cache: 'force-cache' // 静态导出模式下使用force-cache
        });

        if (response.ok) {
          const data = await response.json();
          if (data && data.data) {
            console.log(`[BUILD] Direct API received ${data.data.length} popular games`);
            return data;
          }
        }
      } catch (buildError) {
        console.error('[BUILD] Direct API call failed:', buildError);
      }
    }

    // 客户端或API路由调用
    const response = await tryApiEndpoints(
      `/games/popular?limit=${limit}&page=${page}`
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch popular games: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    return handleAPIError("Popular games fetch failed", error, generateDefaultGames("popular", limit));
  }
}

// 获取单个游戏的详细信息
export async function getGameDetails(gameUrl: string): Promise<ApiResponse<Game>> {
  try {
    // 如果是drive-mad游戏，使用固定的API路径
    if (gameUrl === 'drive-mad') {
      console.log(`[API] Using fixed path for Drive Mad: /games/game/drive-mad`);

      // 在构建时直接调用外部API
      if (typeof window === 'undefined') {
        const directApiUrl = `${API_BASE_URL}/games/game/drive-mad?appname=drivemadstore`;
        console.log(`[BUILD] Direct API call to: ${directApiUrl}`);

        try {
          const response = await fetch(directApiUrl, {
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            },
            cache: 'force-cache' // 静态导出模式下使用force-cache
          });

          if (response.ok) {
            const data = await response.json();
            if (data.status === 'success' && data.data) {
              data.data.image = normalizeImagePath(data.data.image);
              console.log(`[BUILD] Direct API received Drive Mad game details`);
              return data;
            }
          }
        } catch (buildError) {
          console.error('[BUILD] Direct API call failed for Drive Mad:', buildError);
        }
      }

      const response = await tryApiEndpoints(
        `/games/game/drive-mad`,
        {
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
          },
          cache: 'force-cache' // 静态导出模式下使用force-cache
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch Drive Mad game details: ${response.status}`);
      }

      const data = await response.json();

      // 规范化游戏图片URL
      if (data.status === 'success' && data.data) {
        data.data.image = normalizeImagePath(data.data.image);
      }

      return data;
    }

    // 判断gameUrl是否为数字ID
    const isNumericId = /^\d+$/.test(gameUrl);

    // 如果是数字ID，直接使用/games/{game_id}
    if (isNumericId) {
      console.log(`[API] Using direct ID path: /games/${gameUrl}`);

      const response = await tryApiEndpoints(
        `/games/${gameUrl}`,
        {
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
          },
          cache: 'force-cache' // 静态导出模式下使用force-cache
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch game details: ${response.status} for game ID ${gameUrl}`);
      }

      const data = await response.json();

      // 规范化游戏图片URL
      if (data.status === 'success' && data.data) {
        data.data.image = normalizeImagePath(data.data.image);
      }

      return data;
    }
    // 如果是游戏名称，直接使用/games/game/{game_name}接口获取详情
    else {
      console.log(`[API] Using game name path: /games/game/${gameUrl}`);

      const response = await tryApiEndpoints(
        `/games/game/${gameUrl}`,
        {
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
          },
          cache: 'force-cache' // 静态导出模式下使用force-cache
        }
      );

      if (response.ok) {
        const data = await response.json();

        // 规范化游戏图片URL
        if (data.status === 'success' && data.data) {
          data.data.image = normalizeImagePath(data.data.image);
        }

        return data;
      }

      // 如果/games/game/{game_name}接口失败，则尝试搜索方式（作为备选）
      console.log(`[API] Direct game name path failed, trying search method for: ${gameUrl}`);

      // 将游戏名格式化为搜索查询
      const searchQuery = gameUrl.replace(/-/g, ' ');

      // 使用搜索API查找游戏
      const searchResponse = await tryApiEndpoints(
        `/games/search?keyword=${encodeURIComponent(searchQuery)}&limit=1`,
        {
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
          }
        }
      );

      if (!searchResponse.ok) {
        throw new Error(`Failed to search for game: ${searchResponse.status} for query ${searchQuery}`);
      }

      const searchData = await searchResponse.json();

      // 检查是否找到了游戏
      if (searchData.status === 'success' && searchData.data && searchData.data.length > 0) {
        const gameId = searchData.data[0].game_id;
        console.log(`[API] Found game ID: ${gameId} for ${gameUrl}, fetching details`);

        // 使用找到的ID获取游戏详情
        const detailsResponse = await tryApiEndpoints(
          `/games/${gameId}`,
          {
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            },
            cache: 'force-cache' // 静态导出模式下使用force-cache
          }
        );

        if (!detailsResponse.ok) {
          throw new Error(`Failed to fetch game details: ${detailsResponse.status} for game ID ${gameId}`);
        }

        const detailsData = await detailsResponse.json();

        // 规范化游戏图片URL
        if (detailsData.status === 'success' && detailsData.data) {
          detailsData.data.image = normalizeImagePath(detailsData.data.image);
        }

        return detailsData;
      } else {
        throw new Error(`No game found with name: ${gameUrl}`);
      }
    }
  } catch (error) {
    console.error(`Error fetching game details for ${gameUrl}:`, error);

    // 创建默认游戏对象作为备用
    const defaultGame: Game = {
      game_id: Math.floor(Math.random() * 10000),
      game_name: gameUrl,
      name: gameUrl.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' '),
      image: `/images/placeholder-${Math.floor(Math.random() * 5) + 1}.jpg`,
      description: "Game details temporarily unavailable. Please try again later.",
    };

    return {
      status: "error",
      message: error instanceof Error ? error.message : "Unknown error",
      data: defaultGame
    };
  }
}

// 获取分类下的游戏列表
export async function getGamesByCategory(categorySlug: string, limit = 50, offset = 0) {
  console.log(`Fetching games for category slug: ${categorySlug}`);

  // 计算页码（API使用page参数而不是offset）
  const page = Math.floor(offset / limit) + 1;
  console.log(`Using limit=${limit}, offset=${offset}, calculated page=${page}`);

  // 在构建时直接调用外部API
  if (typeof window === 'undefined') {
    // 服务器端/构建时直接调用外部API
    const directApiUrl = `${API_BASE_URL}/games/category/pilot/${categorySlug}?limit=${limit}&page=${page}&appname=drivemadstore`;
    console.log(`[BUILD] Direct API call to: ${directApiUrl}`);

    try {
      const response = await fetch(directApiUrl, {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        },
        cache: 'force-cache' // 静态导出模式下使用force-cache
      });

      if (response.ok) {
        const data = await response.json();
        if (data.status === 'success' && data.data && data.data.length > 0) {
          console.log(`[BUILD] Direct API received ${data.data.length} games for category ${categorySlug}`);
          return data;
        }
      }
    } catch (buildError) {
      console.error(`[BUILD] Direct API call failed for category ${categorySlug}:`, buildError);
    }

    // 如果构建时API调用失败，返回默认数据
    console.log(`[BUILD] Returning default games for category: ${categorySlug}`);
    return {
      status: "success",
      data: generateDefaultGames(categorySlug, limit),
      total: limit * 3,
      message: "Generated default games data for build"
    };
  }

  // 根据分类名返回默认游戏数据
  function generateDefaultGames(category: string, count = limit): Game[] {
    const categoryNames: Record<string, string> = {
      'action': 'Action',
      'adventure': 'Adventure',
      'puzzle': 'Puzzle',
      'racing': 'Racing',
      'sports': 'Sports',
      'strategy': 'Strategy',
      'shooting': 'Shooting',
      'simulation': 'Simulation',
      'rpg': 'RPG',
      'educational': 'Educational',
      'arcade': 'Arcade',
      'casual': 'Casual'
    };

    const catNameLower = category.toLowerCase();
    const processedSlugLower = processedSlug.toLowerCase();

    const catName = categoryNames[catNameLower] ||
                   categoryNames[processedSlugLower] ||
                   'Games';

    const defaultGames: Game[] = [];

    for (let i = 1; i <= count; i++) {
      defaultGames.push({
        game_id: 1000 + i,
        game_name: `${catName.toLowerCase()}-game-${i}`,
        name: `${catName} Game ${i}`,
        image: `/images/placeholder-${(i % 5) + 1}.jpg`,
        plays: Math.floor(Math.random() * 5000),
        rating: (3 + Math.random() * 2).toFixed(1),
        category_name: catName,
        description: `This is a sample ${catName.toLowerCase()} game.`
      });
    }

    return defaultGames;
  }

  // 对特殊分类进行预处理
  let processedSlug = categorySlug;
  if (categorySlug.endsWith('-games')) {
    // 尝试移除"-games"后缀，因为API可能只用基本分类名称
    processedSlug = categorySlug.replace(/-games$/, '');
    console.log(`Processed category slug from ${categorySlug} to ${processedSlug}`);
  }

  // 尝试所有可能的分类格式
  const slugVariations = [
    categorySlug,                         // 原始slug
    categorySlug.toLowerCase(),           // 小写
    // 处理带-games和不带-games的两种可能性
    categorySlug.endsWith('-games') ? categorySlug : `${categorySlug}-games`,
    categorySlug.endsWith('-games') ? categorySlug.replace(/-games$/, '') : categorySlug,
    processedSlug,                        // 预处理的slug（如果有变化）
    processedSlug.toLowerCase(),          // 预处理的slug小写
    categorySlug.replace(/-/g, '_'),      // 破折号替换为下划线
    categorySlug.replace(/_/g, '-'),      // 下划线替换为破折号
    processedSlug.replace(/-/g, '_'),     // 预处理的slug破折号替换为下划线
    processedSlug.replace(/_/g, '-'),     // 预处理的slug下划线替换为破折号
  ];

  // 去重，避免重复请求
  const uniqueSlugs = [...new Set(slugVariations)];
  console.log(`Will try these slug variations: ${uniqueSlugs.join(', ')}`);

  try {
    let result = null;

    // 首先尝试使用category_pilot的API路径（这是正确的API路径）
    for (const slug of uniqueSlugs) {
      console.log(`Trying category/pilot/${slug}`);
      try {
        const response = await tryApiEndpoints(
          `/games/category/pilot/${slug}?limit=${limit}&page=${page}`,
          {
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
          }
        );

        if (response.ok) {
          const data = await response.json();
          console.log(`Success with category/pilot/${slug}:`, data);

          if (data.status === 'success' && data.data && data.data.length > 0) {
            return data;
          }
        }
      } catch (err) {
        console.log(`Error with category/pilot/${slug}:`, err);
        // 继续尝试下一个变体，不抛出错误
      }
    }

    // 然后尝试用分类名称获取
    for (const slug of uniqueSlugs) {
      console.log(`Trying category/name/${slug}`);
      try {
        const response = await tryApiEndpoints(
          `/games/category/name/${slug}?limit=${limit}&page=${page}`,
          {
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
          }
        );

        if (response.ok) {
          const data = await response.json();
          console.log(`Success with category/name/${slug}:`, data);

          if (data.status === 'success' && data.data && data.data.length > 0) {
            return data;
          }
        }
      } catch (err) {
        console.log(`Error with category/name/${slug}:`, err);
        // 继续尝试下一个变体，不抛出错误
      }
    }

    // 然后尝试用分类ID获取
    for (const slug of uniqueSlugs) {
      console.log(`Trying category/${slug}`);
      try {
        const response = await tryApiEndpoints(
          `/games/category/${slug}?limit=${limit}&page=${page}`
        );

        if (response.ok) {
          const data = await response.json();
          console.log(`Success with category/${slug}:`, data);

          if (data.status === 'success' && data.data && data.data.length > 0) {
            return data;
          }

          // 即使没有数据，如果状态是成功，也保存结果（以防所有尝试都失败）
          if (data.status === 'success' && !result) {
            result = data;
          }
        }
      } catch (err) {
        console.log(`Error with category/${slug}:`, err);
        // 继续尝试下一个方法，不抛出错误
      }
    }

    // 然后尝试通用游戏搜索API，将分类作为关键词
    try {
      console.log(`Trying search API with category as keyword: ${categorySlug}`);
      // 构造搜索参数，使用分类作为关键词
      const searchParams = new URLSearchParams();
      searchParams.append("keyword", processedSlug);
      searchParams.append("limit", limit.toString());
      searchParams.append("page", page.toString());

      const response = await tryApiEndpoints(
        `/games/search?${searchParams.toString()}`
      );

      if (response.ok) {
        const data = await response.json();
        console.log(`Success with search API for ${categorySlug}:`, data);

        if (data.status === 'success' && data.data && data.data.length > 0) {
          // 搜索可能返回各种游戏，但我们想要过滤出匹配当前分类的游戏
          // 这里简单返回所有结果，因为搜索已经以分类为关键词
          return data;
        }
      }
    } catch (err) {
      console.log(`Error with search API for ${categorySlug}:`, err);
      // 继续尝试下一个方法，不抛出错误
    }

    // 最后尝试通用分类API（如果存在）
    try {
      console.log(`Trying generic /games/by-category/${categorySlug}`);
      const response = await tryApiEndpoints(
        `/games/by-category/${categorySlug}?limit=${limit}&page=${page}`
      );

      if (response.ok) {
        const data = await response.json();
        console.log(`Success with by-category/${categorySlug}:`, data);
        return data;
      }
    } catch (err) {
      console.log(`Error with by-category/${categorySlug}:`, err);
      // 这是最后一个尝试，错误处理在外层catch块中
    }

    // 返回之前存储的成功结果，或默认生成的游戏数据
    console.log(`All attempts failed for category: ${categorySlug}, generating default games data`);
    return result || {
      status: "success",
      data: generateDefaultGames(categorySlug, limit),
      total: limit * 3, // 假设有足够的游戏支持多页
      message: "Generated default games data"
    };
  } catch (error) {
    console.error(`Error fetching games for category ${categorySlug}:`, error);
    // 返回默认生成的游戏数据
    console.log(`Returning default generated games for category: ${categorySlug}`);
    return {
      status: "success",
      data: generateDefaultGames(categorySlug, limit),
      total: limit * 3, // 假设有足够的游戏支持多页
      message: "Generated default games data after error"
    };
  }
}

// 获取标签下的游戏列表
export async function getGamesByTag(tagSlug: string, limit = 50, offset = 0) {
  console.log(`Fetching games for tag slug: ${tagSlug}`);

  // 计算页码（API使用page参数而不是offset）
  const page = Math.floor(offset / limit) + 1;
  console.log(`Using limit=${limit}, offset=${offset}, calculated page=${page}`);

  // 尝试所有可能的标签路径格式
  const slugVariations = [
    tagSlug,                      // 原始slug
    tagSlug.toLowerCase(),        // 小写
    tagSlug.replace(/-/g, '_'),   // 破折号替换为下划线
    tagSlug.replace(/_/g, '-'),   // 下划线替换为破折号
    tagSlug.replace(/-/g, ''),    // 移除所有破折号
  ];

  // 去重，避免重复请求
  const uniqueSlugs = [...new Set(slugVariations)];
  console.log(`Will try these tag slug variations: ${uniqueSlugs.join(', ')}`);

  try {
    let result = null;

    // 尝试使用标签名称获取
    for (const slug of uniqueSlugs) {
      console.log(`Trying tag/name/${slug}`);
      try {
        const response = await tryApiEndpoints(
          `/games/tag/name/${slug}?limit=${limit}&page=${page}`,
          {
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
          },
          10000 // 增加超时时间到10秒
        );

        if (response.ok) {
          const data = await response.json();
          console.log(`Success with tag/name/${slug}:`, data);

          if (data.status === 'success' && data.data && data.data.length > 0) {
            return data;
          }
        }
      } catch (err) {
        console.log(`Error with tag/name/${slug}:`, err);
      }
    }

    // 尝试别名路径(by-tag)
    for (const slug of uniqueSlugs) {
      console.log(`Trying by-tag/${slug}`);
      try {
        const response = await tryApiEndpoints(
          `/games/by-tag/${slug}?limit=${limit}&page=${page}`
        );

        if (response.ok) {
          const data = await response.json();
          console.log(`Success with by-tag/${slug}:`, data);

          if (data.status === 'success' && data.data && data.data.length > 0) {
            return data;
          }

          // 即使没有数据，如果状态是成功，也保存结果（以防所有尝试都失败）
          if (data.status === 'success' && !result) {
            result = data;
          }
        }
      } catch (err) {
        console.log(`Error with by-tag/${slug}:`, err);
      }
    }

    // 尝试通用搜索API，将标签作为关键词
    try {
      console.log(`Trying search API with tag as keyword: ${tagSlug}`);
      // 构造搜索参数，使用标签作为关键词
      const searchParams = new URLSearchParams();
      searchParams.append("keyword", tagSlug);
      searchParams.append("limit", limit.toString());
      searchParams.append("page", page.toString());

      const response = await tryApiEndpoints(
        `/games/search?${searchParams.toString()}`
      );

      if (response.ok) {
        const data = await response.json();
        console.log(`Success with search API for ${tagSlug}:`, data);

        if (data.status === 'success' && data.data && data.data.length > 0) {
          return data;
        }
      }
    } catch (err) {
      console.log(`Error with search API for ${tagSlug}:`, err);
    }

    // 返回之前存储的成功结果，或一个空结果
    console.log(`All attempts failed for tag: ${tagSlug}`);
    return result || {
      status: "success",
      data: [],
      total: 0
    };
  } catch (error) {
    console.error(`Error fetching games for tag ${tagSlug}:`, error);
    // 返回空结果
    return {
      status: "success",
      data: [],
      total: 0
    };
  }
}

// 点赞响应接口
export interface LikeResponse {
  status: string;
  message: string;
  like_count: number;
  is_liked: boolean;
}

// 点赞某个游戏
export async function toggleGameLike(gameId: number): Promise<LikeResponse> {
  try {
    // 记录当前尝试的路径，便于调试
    console.log(`Attempting to toggle like for game ID: ${gameId}`);

    // 使用正确的API路径 - 通过tryApiEndpoints确保使用代理
    const response = await tryApiEndpoints(
      `/games/toggle-like/${gameId}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      },
      8000 // 8秒超时
    );

    // 记录API响应状态，帮助调试
    console.log(`API response status: ${response.status}`);

    if (!response.ok) {
      if (response.status === 404) {
        // 如果是404错误，模拟点赞响应，并仅依赖本地存储来管理状态
        // 这样在API不可用时页面功能不会受影响
        console.log('API endpoint not found (404). Using local storage only.');

        // 从localStorage获取当前点赞状态
        const likedGames = getLikedGames();
        const currentlyLiked = likedGames.includes(gameId);

        // 切换状态
        const newLikeStatus = !currentlyLiked;

        // 更新本地存储
        updateLikedGame(gameId, newLikeStatus);

        // 返回模拟的响应
        return {
          status: "success",
          message: newLikeStatus ? "Liked successfully" : "Unliked successfully",
          like_count: 0, // 由UI计算
          is_liked: newLikeStatus
        };
      }

      throw new Error(`Failed to toggle game like: ${response.status}`);
    }

    const data = await response.json();
    console.log('API response data:', data);
    return data;
  } catch (error) {
    console.error("Error toggling game like:", error);

    // 在API调用失败时，仍然允许用户在本地切换点赞状态
    // 这样即使API不可用，用户也能有良好的体验
    const likedGames = getLikedGames();
    const currentlyLiked = likedGames.includes(gameId);
    const newLikeStatus = !currentlyLiked;

    // 更新本地存储
    updateLikedGame(gameId, newLikeStatus);

    // 返回错误状态和本地点赞状态
    return {
      status: "error",
      message: "Failed to update like status on server, but updated locally",
      like_count: 0, // 由UI计算
      is_liked: newLikeStatus
    };
  }
}

// 本地存储键
const LIKED_GAMES_KEY = "kizi_liked_games";
const FAVORITED_GAMES_KEY = "favoritedGames";
const RECENT_GAMES_KEY = "kizi_recent_games";

// 最近游戏的接口类型
export interface RecentGame {
  game_id: number;
  game_name: string;
  name: string;
  image: string;
  last_played: number; // 时间戳
  category_name?: string;
  rating?: string;
}

// 记录最近玩过的游戏
export function addRecentGame(game: Game): void {
  if (typeof window === 'undefined' || !game || !game.game_id) {
    return;
  }

  try {
    // 获取当前存储的最近游戏
    const recentGames = getRecentGames();

    // 当前时间戳
    const timestamp = Date.now();

    // 新的游戏记录
    const newRecentGame: RecentGame = {
      game_id: game.game_id,
      game_name: game.game_name,
      name: game.name,
      image: game.image,
      last_played: timestamp,
      category_name: game.category_name,
      rating: game.rating
    };

    // 检查游戏是否已经在列表中
    const existingIndex = recentGames.findIndex(rg => rg.game_id === game.game_id);

    if (existingIndex !== -1) {
      // 移除旧记录
      recentGames.splice(existingIndex, 1);
    }

    // 将新游戏添加到列表头部
    recentGames.unshift(newRecentGame);

    // 限制列表大小，保留最近的20个游戏
    const trimmedList = recentGames.slice(0, 20);

    // 保存到本地存储
    localStorage.setItem(RECENT_GAMES_KEY, JSON.stringify(trimmedList));

    // 分发自定义事件
    try {
      const event = new CustomEvent('recentGamesUpdated', {
        detail: { gameId: game.game_id, recentGames: trimmedList }
      });
      window.dispatchEvent(event);
    } catch (e) {
      console.error('Error dispatching recentGamesUpdated event:', e);
    }
  } catch (error) {
    console.error("Error updating recent games in local storage:", error);
  }
}

// 获取最近玩过的游戏列表
export function getRecentGames(): RecentGame[] {
  if (typeof window === 'undefined') {
    return [];
  }

  try {
    const recentGames = localStorage.getItem(RECENT_GAMES_KEY);
    if (recentGames) {
      return JSON.parse(recentGames);
    }
  } catch (error) {
    console.error("Error retrieving recent games from local storage:", error);
  }

  return [];
}

// 获取本地存储的点赞游戏列表
export function getLikedGames(): number[] {
  if (typeof window === 'undefined') return [];

  const storedLikes = localStorage.getItem(LIKED_GAMES_KEY);
  if (!storedLikes) return [];

  try {
    return JSON.parse(storedLikes);
  } catch (error) {
    console.error("Error parsing liked games from localStorage:", error);
    return [];
  }
}

// 检查游戏是否被点赞
export function isGameLiked(gameId: number): boolean {
  const likedGames = getLikedGames();
  return likedGames.includes(gameId);
}

// 更新本地存储的点赞状态
export function updateLikedGame(gameId: number, isLiked: boolean): void {
  if (typeof window === 'undefined') {
    return;
  }

  let likedGames = getLikedGames();

  if (isLiked) {
    // 如果不在列表中，添加
    if (!likedGames.includes(gameId)) {
      likedGames.push(gameId);
    }
  } else {
    // 如果在列表中，移除
    likedGames = likedGames.filter(id => id !== gameId);
  }

  localStorage.setItem(LIKED_GAMES_KEY, JSON.stringify(likedGames));

  // 分发一个自定义事件，通知同一页面内的其他组件点赞状态已更新
  try {
    const event = new CustomEvent('likesUpdated', {
      detail: { gameId, isLiked, likedGames }
    });
    window.dispatchEvent(event);
  } catch (e) {
    console.error('Error dispatching likesUpdated event:', e);
  }
}

// 获取相关游戏推荐（基于标签）
export async function getRelatedGamesByTags(tags: { id: number; name: string; url: string }[], limit = 6): Promise<Game[]> {
  if (!tags || tags.length === 0) {
    return [];
  }

  try {
    // 先尝试第一个标签
    let allGames: Game[] = [];
    let firstTagResult = null;

    // 第一个标签
    if (tags[0]) {
      console.log(`Fetching games for primary tag: ${tags[0].name}`);
      try {
        firstTagResult = await getGamesByTag(tags[0].url, 10, 0);
        if (firstTagResult.status === 'success' && firstTagResult.data && firstTagResult.data.length > 0) {
          allGames = [...firstTagResult.data];
        }
      } catch (error) {
        console.error(`Error fetching games for primary tag ${tags[0].name}:`, error);
      }
    }

    // 如果第一个标签的游戏不足limit，尝试其他标签补充
    if (allGames.length < limit && tags.length > 1) {
      for (let i = 1; i < tags.length && allGames.length < limit; i++) {
        try {
          console.log(`Fetching games for secondary tag: ${tags[i].name}`);
          const tagResult = await getGamesByTag(tags[i].url, limit - allGames.length, 0);
          if (tagResult.status === 'success' && tagResult.data && tagResult.data.length > 0) {
            // 过滤掉已经存在的游戏（避免重复）
            const newGames = tagResult.data.filter((newGame: Game) =>
              !allGames.some(existingGame => existingGame.game_id === newGame.game_id)
            );
            allGames = [...allGames, ...newGames];
          }
        } catch (error) {
          console.error(`Error fetching games for tag ${tags[i].name}:`, error);
        }
      }
    }

    // 如果相关游戏仍然不足，可以尝试通过分类获取更多游戏
    if (allGames.length < limit && firstTagResult?.data?.[0]?.category_name) {
      try {
        const categoryName = firstTagResult.data[0].category_name;
        const categorySlug = categoryName.toLowerCase().replace(/\s+/g, '-');
        console.log(`Fetching games for category: ${categoryName}`);

        const categoryResult = await getGamesByCategory(categorySlug, limit - allGames.length, 0);
        if (categoryResult.status === 'success' && categoryResult.data && categoryResult.data.length > 0) {
          // 过滤掉已经存在的游戏
          const newGames = categoryResult.data.filter((newGame: Game) =>
            !allGames.some(existingGame => existingGame.game_id === newGame.game_id)
          );
          allGames = [...allGames, ...newGames];
        }
      } catch (error) {
        console.error("Error fetching games by category:", error);
      }
    }

    // 打乱游戏顺序，限制数量，并规范化图片URL
    const games = shuffleArray(allGames).slice(0, limit);

    // 处理每个游戏的图片URL
    return games.map(game => ({
      ...game,
      image: normalizeImagePath(game.image)
    }));
  } catch (error) {
    console.error("Error getting related games:", error);
    return [];
  }
}

// 辅助函数：随机打乱数组
function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

// 收藏响应接口
export interface FavoriteResponse {
  status: string;
  message: string;
  favorite_count: number;
  is_favorited: boolean;
}

// 切换游戏收藏状态
export async function toggleGameFavorite(gameId: number): Promise<FavoriteResponse> {
  try {
    // 检查游戏ID是否有效
    if (!gameId) {
      throw new Error("Invalid game ID");
    }

    // 记录当前尝试的路径，便于调试
    console.log(`Attempting to toggle favorite for game ID: ${gameId}`);

    // 使用正确的API路径 - 通过tryApiEndpoints确保使用代理
    const response = await tryApiEndpoints(
      `/games/toggle-favorite/${gameId}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      },
      8000 // 8秒超时
    );

    // 记录API响应状态，帮助调试
    console.log(`API response status: ${response.status}`);

    if (!response.ok) {
      if (response.status === 404) {
        // 如果是404错误，模拟收藏响应，并仅依赖本地存储来管理状态
        // 这样在API不可用时页面功能不会受影响
        console.log('API endpoint not found (404). Using local storage only.');

        // 从localStorage获取当前收藏状态
        const isCurrentlyFavorited = isGameFavorited(gameId);
        const newFavoritedState = !isCurrentlyFavorited;

        // 更新本地存储
        updateFavoritedGame(gameId, newFavoritedState);

        // 返回模拟的响应
        return {
          status: "success",
          message: newFavoritedState ? "Favorited successfully" : "Unfavorited successfully",
          favorite_count: 0, // 由UI计算
          is_favorited: newFavoritedState
        };
      }

      throw new Error(`Failed to toggle favorite: ${response.status}`);
    }

    const data = await response.json();
    console.log('API response data:', data);

    // 更新本地存储
    updateFavoritedGame(gameId, data.is_favorited);

    return data;
  } catch (error) {
    console.error("Error toggling game favorite:", error);

    // 即使API失败，我们仍然更新本地状态以确保良好的用户体验
    const isCurrentlyFavorited = isGameFavorited(gameId);
    const newFavoritedState = !isCurrentlyFavorited;
    updateFavoritedGame(gameId, newFavoritedState);

    // 返回模拟响应
    return {
      status: "success",
      message: newFavoritedState ? "Favorited successfully" : "Unfavorited successfully",
      favorite_count: 0, // 我们不知道实际数量，所以不更改
      is_favorited: newFavoritedState
    };
  }
}

// 从本地存储获取收藏的游戏ID列表
export function getFavoritedGames(): number[] {
  if (typeof window === 'undefined') {
    return [];
  }

  try {
    const favoritedGames = localStorage.getItem(FAVORITED_GAMES_KEY);
    if (favoritedGames) {
      const parsed = JSON.parse(favoritedGames);
      // 确保返回的是数组
      if (Array.isArray(parsed)) {
        return parsed;
      } else {
        console.error("Favorited games is not an array:", parsed);
        // 如果不是数组，返回空数组
        localStorage.setItem(FAVORITED_GAMES_KEY, JSON.stringify([]));
        return [];
      }
    }
  } catch (error) {
    console.error("Error retrieving favorited games from local storage:", error);
    // 出错时，重置localStorage中的数据
    localStorage.setItem(FAVORITED_GAMES_KEY, JSON.stringify([]));
  }

  return [];
}

// 检查游戏是否被收藏
export function isGameFavorited(gameId: number): boolean {
  try {
    const favoritedGames = getFavoritedGames();
    if (!Array.isArray(favoritedGames)) {
      console.error("getFavoritedGames did not return an array:", favoritedGames);
      return false;
    }
    return favoritedGames.includes(gameId);
  } catch (error) {
    console.error("Error in isGameFavorited:", error);
    return false;
  }
}

// 更新游戏的收藏状态在本地存储中
export function updateFavoritedGame(gameId: number, isFavorited: boolean): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    let favoritedGames = getFavoritedGames();

    // 确保favoritedGames是数组
    if (!Array.isArray(favoritedGames)) {
      console.error("updateFavoritedGame: favoritedGames is not an array:", favoritedGames);
      favoritedGames = [];
    }

    if (isFavorited && !favoritedGames.includes(gameId)) {
      // 添加到收藏
      favoritedGames.push(gameId);
    } else if (!isFavorited && favoritedGames.includes(gameId)) {
      // 从收藏中移除
      favoritedGames = favoritedGames.filter(id => id !== gameId);
    }

    localStorage.setItem(FAVORITED_GAMES_KEY, JSON.stringify(favoritedGames));

    // 分发一个自定义事件，通知同一页面内的其他组件收藏状态已更新
    try {
      const event = new CustomEvent('favoritesUpdated', {
        detail: { gameId, isFavorited, favoritedGames }
      });
      window.dispatchEvent(event);
    } catch (e) {
      console.error('Error dispatching favoritesUpdated event:', e);
    }
  } catch (error) {
    console.error("Error updating favorited game in local storage:", error);
    // 出错时重置存储
    localStorage.setItem(FAVORITED_GAMES_KEY, JSON.stringify([]));
  }
}

// 获取收藏的游戏详情
export async function getFavoritedGameDetails(): Promise<Game[]> {
  const favoritedGameIds = getFavoritedGames();

  // 确保favoritedGameIds是数组
  if (!Array.isArray(favoritedGameIds)) {
    console.error("getFavoritedGameDetails: favoritedGameIds is not an array:", favoritedGameIds);
    return [];
  }

  if (favoritedGameIds.length === 0) {
    return [];
  }

  try {
    console.log(`Attempting to fetch ${favoritedGameIds.length} favorited games`);

    // 使用Promise.all并行获取所有游戏详情
    const gamePromises = favoritedGameIds.map(async (gameId) => {
      try {
        // 尝试获取游戏详情
        const gameDetailResponse = await getGameById(gameId);
        if (gameDetailResponse && gameDetailResponse.data) {
          return gameDetailResponse.data;
        }
      } catch (err) {
        console.error(`Error fetching details for game ${gameId}:`, err);
      }

      // 如果获取失败，返回一个基础的游戏对象
      return createPlaceholderGame(gameId);
    });

    // 等待所有Promise完成
    const results: (Game | null)[] = await Promise.all(gamePromises);

    // 过滤掉null结果
    return results.filter((item): item is Game => item !== null);
  } catch (error) {
    console.error("Error fetching favorited game details:", error);

    // 在完全失败的情况下，为所有收藏的游戏ID创建占位符对象
    return favoritedGameIds.map(gameId => createPlaceholderGame(gameId));
  }
}

// 尝试根据ID获取单个游戏详情
async function getGameById(gameId: number): Promise<ApiResponse<Game> | null> {
  try {
    const response = await tryApiEndpoints(
      `/games/${gameId}`,
      {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      },
      5000 // 5秒超时
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch game ${gameId}: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error fetching game ${gameId}:`, error);
    return null;
  }
}

// 创建一个占位符游戏对象
function createPlaceholderGame(gameId: number): Game {
  return {
    game_id: gameId,
    game_name: `game-${gameId}`,
    name: `Favorited Game ${gameId}`,
    image: 'https://via.placeholder.com/300x300?text=Game+Image',
    category_name: 'Favorite',
    rating: '4.5',
    description: 'This is a game you favorited. Details are currently unavailable.'
  };
}

// 定义页面描述接口
export interface PageDescription {
  page_name: string;
  page_url: string;
  description: string;
  has_content: string;
  content_value: string;
}

export interface PageDescriptionResponse {
  status: string;
  data: PageDescription;
}

// 获取页面描述
export async function getPageDescription(pageUrl: string = "/"): Promise<PageDescriptionResponse> {
  console.log(`[API] Fetching page description for URL: ${pageUrl}`);

  try {
    // 使用tryApiEndpoints函数获取页面描述，而不是直接调用外部API
    // 这样会通过本地代理发送请求，避免CORS问题
    const response = await tryApiEndpoints(
      `/pages/?page_url=${encodeURIComponent(pageUrl)}`,
      {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        },
        next: { revalidate: 3600 } // 缓存1小时
      }
    );

    if (!response.ok) {
      console.error(`[API] Failed to fetch page description: ${response.status} ${response.statusText}`);
      throw new Error(`Failed to fetch page description: ${response.status}`);
    }

    const data = await response.json();
    console.log(`[API] Successfully received page description:`, data);

    // 直接返回API数据，不再使用默认内容覆盖
    if (data.status === "success" && data.data) {
      return data;
    }

    // 只有当API返回的数据格式不正确或为空时才使用默认内容
    console.warn(`[API] API returned invalid or empty data for ${pageUrl}, using fallback content`);

    // 为特定页面提供默认内容（作为备选）
    if (pageUrl === "/about") {
      data.data = {
        page_name: "about",
        page_url: "/about",
        description: "About Drive Mad",
        has_content: "1",
        content_value: "<p>Welcome to Drive Mad! We are dedicated to providing the best online gaming experience for players around the world.</p><p>At Drive Mad, we carefully select and host a wide variety of high-quality games that are free to play and accessible directly in your browser. Whether you're into action, adventure, puzzles, or racing games, we have something for everyone.</p><p>Our mission is to make gaming fun, accessible, and safe for all ages. We regularly update our collection with new and exciting titles to keep the experience fresh and engaging.</p><p>Thank you for choosing Drive Mad as your gaming destination!</p>"
      };
    } else if (pageUrl === "/privacy") {
      data.data = {
        page_name: "privacy",
        page_url: "/privacy",
        description: "Privacy Policy",
        has_content: "1",
        content_value: "<p>This Privacy Policy describes how your personal information is collected, used, and shared when you visit our website.</p><p>We are committed to protecting your privacy and ensuring that your personal information is handled securely.</p>"
      };
    } else if (pageUrl === "/terms") {
      data.data = {
        page_name: "terms",
        page_url: "/terms",
        description: "Terms of Service",
        has_content: "1",
        content_value: "<p>By accessing our website, you agree to be bound by these Terms of Service and to use the website in accordance with these Terms.</p><p>We reserve the right to update or modify these Terms at any time without prior notice.</p>"
      };
    } else if (pageUrl === "/contact") {
      data.data = {
        page_name: "contact",
        page_url: "/contact",
        description: "Contact Us",
        has_content: "1",
        content_value: "<p>Have questions, feedback, or need assistance? We'd love to hear from you!</p><p>You can reach our support <NAME_EMAIL>.</p><p>For business inquiries, <NAME_EMAIL>.</p>"
      };
    } else if (pageUrl === "/faq") {
      data.data = {
        page_name: "faq",
        page_url: "/faq",
        description: "Frequently Asked Questions",
        has_content: "1",
        content_value: "<p><strong>Q: Are all games free to play?</strong></p><p>A: Yes, all games on Drive Mad are completely free to play.</p><p><strong>Q: Do I need to create an account to play?</strong></p><p>A: No, you can play most games without creating an account. However, creating an account allows you to save your progress and favorite games.</p>"
      };
    }

    return data;
  } catch (error) {
    console.error("[API] Error fetching page description:", error);

    // 根据页面URL返回适当的默认内容
    let defaultDescription = "Welcome to Drive Mad! Play the best online games for free.";
    let defaultContent = "At Drive Mad, we serve up thousands of free online games to play now, from thrilling action games to cool racing games. You can access all of our games via your browser window, no downloads required!";
    let pageName = "home";

    // 为特定页面提供默认内容
    if (pageUrl === "/about") {
      pageName = "about";
      defaultDescription = "About Drive Mad";
      defaultContent = "<p>Welcome to Drive Mad! We are dedicated to providing the best online gaming experience for players around the world.</p><p>At Drive Mad, we carefully select and host a wide variety of high-quality games that are free to play and accessible directly in your browser. Whether you're into action, adventure, puzzles, or racing games, we have something for everyone.</p><p>Our mission is to make gaming fun, accessible, and safe for all ages. We regularly update our collection with new and exciting titles to keep the experience fresh and engaging.</p><p>Thank you for choosing Drive Mad as your gaming destination!</p>";
    } else if (pageUrl === "/privacy") {
      pageName = "privacy";
      defaultDescription = "Privacy Policy";
      defaultContent = "<p>This Privacy Policy describes how your personal information is collected, used, and shared when you visit our website.</p><p>We are committed to protecting your privacy and ensuring that your personal information is handled securely.</p>";
    } else if (pageUrl === "/terms") {
      pageName = "terms";
      defaultDescription = "Terms of Service";
      defaultContent = "<p>By accessing our website, you agree to be bound by these Terms of Service and to use the website in accordance with these Terms.</p><p>We reserve the right to update or modify these Terms at any time without prior notice.</p>";
    } else if (pageUrl === "/contact") {
      pageName = "contact";
      defaultDescription = "Contact Us";
      defaultContent = "<p>Have questions, feedback, or need assistance? We'd love to hear from you!</p><p>You can reach our support <NAME_EMAIL>.</p><p>For business inquiries, <NAME_EMAIL>.</p>";
    } else if (pageUrl === "/faq") {
      pageName = "faq";
      defaultDescription = "Frequently Asked Questions";
      defaultContent = "<p><strong>Q: Are all games free to play?</strong></p><p>A: Yes, all games on Drive Mad are completely free to play.</p><p><strong>Q: Do I need to create an account to play?</strong></p><p>A: No, you can play most games without creating an account. However, creating an account allows you to save your progress and favorite games.</p>";
    }

    console.log(`[API] Returning default content for ${pageUrl}`);

    return {
      status: "success", // 返回success以避免页面显示错误
      data: {
        page_name: pageName,
        page_url: pageUrl,
        description: defaultDescription,
        has_content: "1",
        content_value: defaultContent
      }
    };
  }
}

// 定义通用API响应类型
export interface ApiResponse<T> {
  status: string;
  data: T;
  message?: string;
}

// 工具函数：规范化图片URL路径
export function normalizeImagePath(imagePath: string): string {
  if (!imagePath) return '';

  // 如果已经是绝对URL，保持不变
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }

  // 如果路径以images/开头，添加前导斜杠
  if (imagePath.startsWith('images/')) {
    return `/${imagePath}`;
  }

  // 如果路径包含 /games/images/，修正为正确路径
  if (imagePath.includes('/games/images/')) {
    return imagePath.replace('/games/images/', '/images/');
  }

  // 确保路径以/开头
  if (!imagePath.startsWith('/')) {
    return `/${imagePath}`;
  }

  return imagePath;
}

// 通用API错误处理函数
function handleAPIError(context: string, error: any, fallbackData: any = []) {
  const errorMessage = error instanceof Error ? error.message : String(error);
  console.error(`[API ERROR] ${context}: ${errorMessage}`);

  // 检查是否是404错误
  const is404 = errorMessage.includes('404') || errorMessage.includes('Not Found');
  if (is404) {
    console.warn(`[API] ${context} - 404 Not Found, using fallback data`);
  }

  // 返回标准化错误响应
  return {
    status: "error",
    data: fallbackData,
    error: errorMessage,
    total: Array.isArray(fallbackData) ? fallbackData.length : 0
  };
}

// 生成默认游戏数据
function generateDefaultGames(category: string = "general", count: number = 10): Game[] {
  const categoryNameMap: Record<string, string> = {
    'action': 'Action',
    'adventure': 'Adventure',
    'puzzle': 'Puzzle',
    'racing': 'Racing',
    'sports': 'Sports',
    'strategy': 'Strategy',
    'shooting': 'Shooting',
    'simulation': 'Simulation',
    'rpg': 'RPG',
    'educational': 'Educational',
    'arcade': 'Arcade',
    'casual': 'Casual',
    // 添加其他可能的分类
    'general': 'Popular'
  };

  const catName = categoryNameMap[category.toLowerCase()] || 'Games';
  const defaultGames: Game[] = [];

  for (let i = 1; i <= count; i++) {
    defaultGames.push({
      game_id: 1000 + i,
      game_name: `${category.toLowerCase()}-game-${i}`,
      name: `${catName} Game ${i}`,
      image: `/images/placeholder-${(i % 5) + 1}.jpg`,
      plays: Math.floor(Math.random() * 5000),
      rating: (3 + Math.random() * 2).toFixed(1),
      category_name: catName,
      description: `This is a sample ${catName.toLowerCase()} game.`
    });
  }

  return defaultGames;
}

// 工具函数：处理分类图片路径
export function processImagePath(imagePath: string, categoryPilot?: string): string {
  if (!imagePath || imagePath.includes('undefined')) {
    // 如果没有图片或图片路径包含undefined，则使用默认图片
    return categoryPilot
      ? `/images/cat/${categoryPilot}.jpg`
      : '/images/placeholder-category.jpg';
  }

  // 如果路径已经包含/images/cat/，则直接返回
  if (imagePath.includes('/images/cat/')) {
    return imagePath;
  }

  // 如果路径只是/cat/xxx.jpg，则添加/images前缀
  if (imagePath.startsWith('/cat/')) {
    return `/images${imagePath}`;
  }

  // 如果路径是cat/xxx.jpg（没有前导斜杠），则添加/images/
  if (imagePath.startsWith('cat/')) {
    return `/images/${imagePath}`;
  }

  // 如果路径是完整的http URL，则直接返回
  if (imagePath.startsWith('http')) {
    return imagePath;
  }

  // 其他情况，确保路径以/开头
  return imagePath.startsWith('/') ? imagePath : `/${imagePath}`;
}