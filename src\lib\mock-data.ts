import type { GameCardProps } from "@/components/game-card/GameCard";

export const featuredGames = [
  {
    id: "traffic-tour",
    title: "Traffic Tour",
    imageUrl: "https://ext.same-assets.com/130919172/1364350017.webp",
    category: "Racing",
  },
  {
    id: "star-stable",
    title: "Star Stable",
    imageUrl: "https://ext.same-assets.com/130919172/1040633098.webp",
    category: "Social",
  },
  {
    id: "water-color-sort",
    title: "Water Color Sort",
    imageUrl: "https://ext.same-assets.com/130919172/1108756274.webp",
    category: "Puzzle",
  },
];

export const newReleases: GameCardProps[] = [
  {
    id: "bubble-shooter-billiard-pool",
    title: "Bubble Shooter Billiard Pool",
    imageUrl: "https://ext.same-assets.com/130919172/3684382212.webp",
    category: "Puzzle",
    plays: 548,
  },
  {
    id: "office-brawl-room-smash",
    title: "Office Brawl Room Smash",
    imageUrl: "https://ext.same-assets.com/130919172/636406006.webp",
    category: "Action",
    plays: 398,
  },
  {
    id: "bubble-mania-shooter",
    title: "Bubble Mania Shooter",
    imageUrl: "https://ext.same-assets.com/130919172/591862448.webp",
    category: "Puzzle",
    plays: 503,
  },
  {
    id: "marathon-race-io",
    title: "Marathon Race IO",
    imageUrl: "https://ext.same-assets.com/130919172/1364350017.webp",
    category: "Racing",
    plays: 557,
  },
  {
    id: "fast-ball-jump",
    title: "Fast Ball Jump",
    imageUrl: "https://ext.same-assets.com/130919172/1040633098.webp",
    category: "Arcade",
    plays: 1287,
  },
  {
    id: "doggi",
    title: "Doggi",
    imageUrl: "https://ext.same-assets.com/130919172/1108756274.webp",
    category: "Pet",
    plays: 789,
  },
  {
    id: "knife-master-ball-racing",
    title: "Knife Master: Ball Racing",
    imageUrl: "https://ext.same-assets.com/130919172/3684382212.webp",
    category: "Racing",
    plays: 1062,
  },
  {
    id: "idle-dairy-farm-tycoon",
    title: "Idle Dairy Farm Tycoon",
    imageUrl: "https://ext.same-assets.com/130919172/636406006.webp",
    category: "Simulation",
    plays: 741,
  },
  {
    id: "power-light",
    title: "Power Light",
    imageUrl: "https://ext.same-assets.com/130919172/591862448.webp",
    category: "Puzzle",
    plays: 784,
  },
  {
    id: "dynamons-connect",
    title: "Dynamons Connect",
    imageUrl: "https://ext.same-assets.com/130919172/1364350017.webp",
    category: "Puzzle",
    plays: 1586,
  },
  {
    id: "bubble-rush",
    title: "Bubble Rush",
    imageUrl: "https://ext.same-assets.com/130919172/1040633098.webp",
    category: "Puzzle",
    plays: 1213,
  },
  {
    id: "bus-parking-out",
    title: "Bus Parking Out",
    imageUrl: "https://ext.same-assets.com/130919172/1108756274.webp",
    category: "Parking",
    plays: 1529,
  },
];

export const topFavoriteGames: GameCardProps[] = [
  {
    id: "fireboy-and-watergirl-the-forest-temple",
    title: "Fireboy & Watergirl 1 - The Forest Temple",
    imageUrl: "https://ext.same-assets.com/130919172/3684382212.webp",
    category: "2 Player",
    plays: 5782354,
  },
  {
    id: "fireboy-and-watergirl-the-crystal-temple",
    title: "Fireboy & Watergirl 4 - The Crystal Temple",
    imageUrl: "https://ext.same-assets.com/130919172/636406006.webp",
    category: "2 Player",
    plays: 3245879,
  },
  {
    id: "slitherio",
    title: "slither.io",
    imageUrl: "https://ext.same-assets.com/130919172/591862448.webp",
    category: "IO",
    plays: 8934521,
  },
  {
    id: "fireboy-and-watergirl-the-light-temple",
    title: "Fireboy & Watergirl 2 - The Light Temple",
    imageUrl: "https://ext.same-assets.com/130919172/1364350017.webp",
    category: "2 Player",
    plays: 4583921,
  },
  {
    id: "money-movers",
    title: "Money Movers 1",
    imageUrl: "https://ext.same-assets.com/130919172/1040633098.webp",
    category: "2 Player",
    plays: 3871542,
  },
  {
    id: "fireboy-and-watergirl-the-ice-temple",
    title: "Fireboy & Watergirl 3 - The Ice Temple",
    imageUrl: "https://ext.same-assets.com/130919172/1108756274.webp",
    category: "2 Player",
    plays: 2984715,
  },
];

export const gameSeries = [
  {
    id: "moto-x3m",
    title: "Moto X3M",
    imageUrl: "https://ext.same-assets.com/130919172/3684382212.webp",
  },
  {
    id: "bob-the-robber",
    title: "Bob The Robber",
    imageUrl: "https://ext.same-assets.com/130919172/636406006.webp",
  },
  {
    id: "kizi",
    title: "Kizi",
    imageUrl: "https://ext.same-assets.com/130919172/591862448.webp",
  },
  {
    id: "wheely",
    title: "Wheely",
    imageUrl: "https://ext.same-assets.com/130919172/1364350017.webp",
  },
  {
    id: "fireboy-and-watergirl",
    title: "Fireboy & Watergirl",
    imageUrl: "https://ext.same-assets.com/130919172/1040633098.webp",
  },
  {
    id: "snail-bob",
    title: "Snail Bob",
    imageUrl: "https://ext.same-assets.com/130919172/1108756274.webp",
  },
  {
    id: "dynamons",
    title: "Dynamons",
    imageUrl: "https://ext.same-assets.com/130919172/3684382212.webp",
  },
  {
    id: "money-movers",
    title: "Money Movers",
    imageUrl: "https://ext.same-assets.com/130919172/636406006.webp",
  },
  {
    id: "3-pandas",
    title: "3 Pandas",
    imageUrl: "https://ext.same-assets.com/130919172/591862448.webp",
  },
  {
    id: "bomb-it",
    title: "Bomb It",
    imageUrl: "https://ext.same-assets.com/130919172/1364350017.webp",
  },
  {
    id: "papa-louie",
    title: "Papa Louie",
    imageUrl: "https://ext.same-assets.com/130919172/1040633098.webp",
  },
  {
    id: "princess-juliet",
    title: "Princess Juliet",
    imageUrl: "https://ext.same-assets.com/130919172/1108756274.webp",
  },
];
