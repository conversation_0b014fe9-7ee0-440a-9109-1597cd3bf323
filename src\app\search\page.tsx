// 注意：对于静态导出，我们必须配置导出选项
export const dynamic = 'force-dynamic';

import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import SearchClientPage from "@/components/search/SearchClientPage";
import { Metadata } from "next";

// 最常见的搜索词的元数据
export function generateMetadata(): Metadata {
  return {
    title: "Search Games - Drive Mad",
    description: "Search for your favorite games on Drive Mad website",
  };
}

export default function SearchPage() {
  return (
    <div className="flex flex-col min-h-screen" style={{ backgroundColor: 'white' }}>
      <Header />
      <main className="flex-grow container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <SearchClientPage />
        </div>
      </main>
      <Footer />
    </div>
  );
} 