"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { getGameDetails, Game, toggleGameLike, isGameLiked, updateLikedGame, getRelatedGamesByTags, toggleGameFavorite, isGameFavorited, updateFavoritedGame, normalizeImagePath } from "@/lib/apiService";
import { useRouter } from "next/navigation";

interface GameDetailsClientProps {
  gameName: string;
  initialData: Game | null;
}

// 增强Game接口以解决TypeScript错误
interface EnhancedGame extends Game {
  instructions?: string;
}

export default function GameDetailsClient({ gameName, initialData }: GameDetailsClientProps) {
  const [game, setGame] = useState<EnhancedGame | null>(initialData as EnhancedGame | null);
  const [isLoading, setIsLoading] = useState(!initialData);
  const [error, setError] = useState<string | null>(null);
  const [isLiked, setIsLiked] = useState(false);
  const [likeCount, setLikeCount] = useState(initialData?.like_count || 0);
  const [isLikeProcessing, setIsLikeProcessing] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);
  const [favoriteCount, setFavoriteCount] = useState(initialData?.favorite_count || 0);
  const [isFavoriteProcessing, setIsFavoriteProcessing] = useState(false);
  const [relatedGames, setRelatedGames] = useState<Game[]>([]);
  const [isLoadingRelated, setIsLoadingRelated] = useState(false);
  const [shareUrl, setShareUrl] = useState('');
  const [showShareTooltip, setShowShareTooltip] = useState(false);
  const router = useRouter();

  // 设置分享URL
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setShareUrl(window.location.href);
    }
  }, []);

  // 初始化时从本地存储读取点赞和收藏状态
  useEffect(() => {
    if (game?.game_id) {
      const liked = isGameLiked(game.game_id);
      setIsLiked(liked);

      const favorited = isGameFavorited(game.game_id);
      setIsFavorited(favorited);
    }
  }, [game?.game_id]);

  // 获取游戏详情
  useEffect(() => {
    // 如果没有初始数据或者gameName变化了，才从客户端获取数据
    if (!initialData || !game) {
      async function fetchGameDetails() {
        try {
          setIsLoading(true);
          const result = await getGameDetails(gameName);

          if (result.status === 'success' && result.data) {
            setGame(result.data as EnhancedGame);
            setLikeCount(result.data.like_count || 0);
            setFavoriteCount(result.data.favorite_count || 0);
            setError(null);
          } else {
            setError("Game not found");
          }
        } catch (err) {
          console.error("Error fetching game details:", err);
          setError("Failed to load game details");
        } finally {
          setIsLoading(false);
        }
      }

      if (gameName) {
        fetchGameDetails();
      }
    }
  }, [gameName, initialData, game]);

  // 获取相关游戏
  useEffect(() => {
    if (game && game.tags && Array.isArray(game.tags) && game.tags.length > 0) {
      async function fetchRelatedGames() {
        try {
          setIsLoadingRelated(true);
          // 使用非空断言，因为我们已经在if条件中检查了game不为null
          const tags = game!.tags as { id: number; name: string; url: string; }[];
          const related = await getRelatedGamesByTags(tags, 6);

          // 过滤掉当前游戏
          const gameId = game!.game_id;
          const filteredGames = gameId ? related.filter(g => g.game_id !== gameId) : related;
          setRelatedGames(filteredGames.slice(0, 6));
        } catch (error) {
          console.error("Error fetching related games:", error);
        } finally {
          setIsLoadingRelated(false);
        }
      }

      fetchRelatedGames();
    }
  }, [game?.tags, game?.game_id]);

  // 处理点赞/取消点赞
  const handleToggleLike = async () => {
    if (!game || !game.game_id || isLikeProcessing) return;

    try {
      setIsLikeProcessing(true);

      // 更新本地状态，使界面立即响应
      const newLikedState = !isLiked;
      setIsLiked(newLikedState);

      // 更新本地点赞数
      const newLikeCount = newLikedState ? likeCount + 1 : Math.max(0, likeCount - 1);
      setLikeCount(newLikeCount);

      // 调用API
      console.log(`Toggling like for game: ${game.name} (ID: ${game.game_id})`);
      const result = await toggleGameLike(game.game_id);
      console.log('Toggle like result:', result);

      if (result.status === 'success') {
        // 使用API返回的数值更新状态
        if (result.like_count > 0) {
          // 只有当API返回有效的点赞数时才使用
          setLikeCount(result.like_count);
        }
        setIsLiked(result.is_liked);

        // 更新本地存储
        updateLikedGame(game.game_id, result.is_liked);
      } else {
        // API失败但本地状态依然更新
        // 不需要回滚UI状态，因为我们已经在API服务中更新了本地存储
        console.log('API failed but local like state was updated');
      }
    } catch (error) {
      console.error("Failed to toggle like:", error);
      // 即使在完全失败的情况下，仍保持本地状态更新
      // 提供更流畅的用户体验
    } finally {
      setIsLikeProcessing(false);
    }
  };

  // 处理收藏/取消收藏
  const handleToggleFavorite = async () => {
    if (!game || !game.game_id || isFavoriteProcessing) return;

    try {
      setIsFavoriteProcessing(true);

      // 更新本地状态，使界面立即响应
      const newFavoritedState = !isFavorited;
      setIsFavorited(newFavoritedState);

      // 更新本地收藏数
      const newFavoriteCount = newFavoritedState ? favoriteCount + 1 : Math.max(0, favoriteCount - 1);
      setFavoriteCount(newFavoriteCount);

      // 调用API
      console.log(`Toggling favorite for game: ${game.name} (ID: ${game.game_id})`);
      const result = await toggleGameFavorite(game.game_id);
      console.log('Toggle favorite result:', result);

      if (result.status === 'success') {
        // 使用API返回的数值更新状态
        if (result.favorite_count > 0) {
          // 只有当API返回有效的收藏数时才使用
          setFavoriteCount(result.favorite_count);
        }
        setIsFavorited(result.is_favorited);

        // 更新本地存储
        updateFavoritedGame(game.game_id, result.is_favorited);
      } else {
        // API失败但本地状态依然更新
        console.log('API failed but local favorite state was updated');
      }
    } catch (error) {
      console.error("Failed to toggle favorite:", error);
      // 即使在完全失败的情况下，仍保持本地状态更新
      // 提供更流畅的用户体验
    } finally {
      setIsFavoriteProcessing(false);
    }
  };

  // 处理分享功能
  const handleShare = async () => {
    if (!game) return;

    // 构建分享数据
    const shareData = {
      title: `${game.name} - Play Online`,
      text: game.description ? game.description.replace(/<\/?[^>]+(>|$)/g, "").substring(0, 100) + '...' : `Play ${game.name} online for free!`,
      url: shareUrl,
    };

    try {
      // 检查浏览器是否支持Web Share API
      if (navigator.share && navigator.canShare && navigator.canShare(shareData)) {
        // 使用Web Share API (移动端更兼容)
        await navigator.share(shareData);
        console.log('Game shared successfully');
      } else {
        // 备用方案：复制链接到剪贴板
        await navigator.clipboard.writeText(shareUrl);
        setShowShareTooltip(true);

        // 3秒后隐藏提示
        setTimeout(() => {
          setShowShareTooltip(false);
        }, 3000);
      }
    } catch (error) {
      console.error('Error sharing game:', error);
    }
  };

  // 处理HTML内容，确保标签被正确解析
  const sanitizeHtml = (html: string | undefined | null): string => {
    if (!html) return '';

    // 如果HTML被转义了（例如 &lt; 代替 <），将其转回来
    let sanitized = html.replace(/&lt;/g, '<').replace(/&gt;/g, '>');

    return sanitized;
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 animate-pulse">
        <div className="w-full h-[250px] bg-blue-200 rounded-md mb-6"></div>
        <div className="h-8 bg-gray-200 rounded w-1/2 mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-1/4 mb-6"></div>
        <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-6"></div>
        <div className="flex space-x-2 mb-6">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="h-8 bg-blue-100 rounded-full px-4 w-20"></div>
          ))}
        </div>
        <div className="h-12 bg-blue-200 rounded-lg w-40 mx-auto"></div>
      </div>
    );
  }

  if (error || !game) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 text-center">
        <h1 className="text-2xl font-bold text-red-600 mb-4">Game Not Found</h1>
        <p className="text-gray-600 mb-6">Sorry, the game you are looking for is not available.</p>
        <Link href="/" className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded-md transition-colors">
          Back to Home
        </Link>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      {/* 游戏内容区域 */}
      <div className="max-w-7xl mx-auto p-4 sm:p-6">
        {/* 游戏标题区域 */}
        <div className="text-center mb-8">
          <div className="w-full max-w-md mx-auto mb-4 relative aspect-square">
            <Image
              src={normalizeImagePath(game.image)}
              alt={game.name}
              fill
              className="object-cover rounded-lg"
              sizes="(max-width: 768px) 100vw, 400px"
              priority
            />
          </div>

          <h1 className="text-3xl font-bold text-gray-900 mb-4">{game.name}</h1>

          {/* 游戏标签 */}
          <div className="flex flex-wrap items-center justify-center gap-2 mb-6">
            {game.category_name && (
              <Link
                href={`/categories/${game.category_name.toLowerCase().replace(/\s+/g, '-')}`}
                className="bg-blue-50 text-blue-600 text-sm font-medium px-4 py-1.5 rounded-full hover:bg-blue-100 transition-colors"
              >
                {game.category_name}
              </Link>
            )}
            {game.tags && Array.isArray(game.tags) && game.tags.slice(0, 3).map(tag => (
              <Link
                key={tag.id}
                href={`/tags/${tag.url}`}
                className="bg-blue-50 text-blue-600 text-sm font-medium px-4 py-1.5 rounded-full hover:bg-blue-100 transition-colors"
              >
                {tag.name}
              </Link>
            ))}
            {game.tags && typeof game.tags === 'string' && game.tags.split(',').slice(0, 3).map((tagName, index) => (
              <span
                key={index}
                className="bg-blue-50 text-blue-600 text-sm font-medium px-4 py-1.5 rounded-full"
              >
                {tagName.trim()}
              </span>
            ))}
            {game.rating && (
              <span className="bg-yellow-100 text-yellow-800 text-sm font-medium px-4 py-1.5 rounded-full flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
                {game.rating}
              </span>
            )}
          </div>

          {/* Play Now 按钮 */}
          <button
            onClick={() => router.push(`/games/${gameName}/play`)}
            className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-md transition-all duration-200 flex items-center justify-center mx-auto mb-8 hover:scale-105 transform"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
            </svg>
            Play Now
          </button>

          {/* 互动按钮 - 修改为水平布局，数字显示在图标旁边 */}
          <div className="flex justify-center items-center space-x-8 mb-8">
            {/* 点赞按钮 */}
            <button
              onClick={handleToggleLike}
              disabled={isLikeProcessing}
              className="flex items-center justify-center relative px-4 py-2 rounded-md border border-gray-200 hover:bg-gray-50 transition-colors"
            >
              {isLikeProcessing && (
                <span className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-60 rounded-md">
                  <div className="w-4 h-4 border-t-2 border-b-2 border-blue-600 rounded-full animate-spin"></div>
                </span>
              )}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className={`h-6 w-6 ${isLiked ? 'text-blue-600' : 'text-gray-500'}`}
                viewBox="0 0 20 20"
                fill={isLiked ? "currentColor" : "none"}
                stroke={isLiked ? "none" : "currentColor"}
                strokeWidth="1.5"
              >
                <path d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z" />
              </svg>
              <span className="ml-2 font-medium text-gray-700">{likeCount.toLocaleString()}</span>
            </button>

            {/* 收藏按钮 */}
            <button
              onClick={handleToggleFavorite}
              disabled={isFavoriteProcessing}
              className="flex items-center justify-center relative px-4 py-2 rounded-md border border-gray-200 hover:bg-gray-50 transition-colors"
            >
              {isFavoriteProcessing && (
                <span className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-60 rounded-md">
                  <div className="w-4 h-4 border-t-2 border-b-2 border-blue-600 rounded-full animate-spin"></div>
                </span>
              )}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className={`h-6 w-6 ${isFavorited ? 'text-blue-600' : 'text-gray-500'}`}
                viewBox="0 0 20 20"
                fill={isFavorited ? "currentColor" : "none"}
                stroke={isFavorited ? "none" : "currentColor"}
                strokeWidth="1.5"
              >
                <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
              </svg>
              <span className="ml-2 font-medium text-gray-700">{favoriteCount.toLocaleString()}</span>
            </button>

            {/* 分享按钮 - 添加分享功能 */}
            <div className="relative">
              <button
                onClick={handleShare}
                className="flex items-center justify-center p-2 rounded-full border border-gray-200 hover:bg-gray-50 transition-colors"
                aria-label="Share game"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6 text-gray-500"
                  viewBox="0 0 20 20"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                >
                  <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z" />
                </svg>
              </button>

              {/* 分享成功提示 */}
              {showShareTooltip && (
                <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs py-1 px-2 rounded whitespace-nowrap">
                  Link copied to clipboard
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 rotate-45 w-2 h-2 bg-gray-800"></div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 游戏描述内容 */}
        <div className="border-t border-gray-100 pt-8">
          {/* How to Play 模块 - 添加在About This Game上方 */}
          {game.instructions && (
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">How to Play</h2>
              <div className="bg-blue-50 rounded-lg p-6 border-l-4 border-blue-500">
                <div
                  className="prose prose-blue max-w-none text-gray-700 leading-relaxed"
                  dangerouslySetInnerHTML={{ __html: sanitizeHtml(game.instructions) }}
                />
              </div>
            </div>
          )}

          <h2 className="text-2xl font-bold text-gray-900 mb-4">About This Game</h2>
          <div className="prose prose-blue max-w-none text-gray-600 mb-8">
            <div
              className="text-lg leading-relaxed"
              dangerouslySetInnerHTML={{ __html: sanitizeHtml(game.description) }}
            />
          </div>

          {/* 相关游戏推荐 */}
          <div className="mt-12 mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">You Might Also Like</h2>

            {isLoadingRelated ? (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-4">
                {Array.from({ length: 6 }).map((_, i) => (
                  <div key={i} className="bg-gray-100 rounded-lg animate-pulse">
                    <div className="aspect-square w-full bg-gray-200 rounded-t-lg"></div>
                    <div className="p-2">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : relatedGames.length > 0 ? (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-4">
                {relatedGames.map(relatedGame => (
                  <Link
                    key={relatedGame.game_id}
                    href={`/games/${relatedGame.game_name}`}
                    className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden group"
                  >
                    <div className="aspect-square w-full relative overflow-hidden">
                      <Image
                        src={normalizeImagePath(relatedGame.image)}
                        alt={relatedGame.name}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                        sizes="(max-width: 768px) 50vw, 120px"
                      />
                    </div>
                    <div className="p-2">
                      <h3 className="font-medium text-sm text-gray-900 truncate group-hover:text-blue-600 transition-colors">{relatedGame.name}</h3>
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">No related games found</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}